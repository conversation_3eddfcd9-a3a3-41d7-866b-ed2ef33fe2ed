import 'package:flutter/material.dart';
import '../../core/services/token_service.dart';
import '../../core/services/api_service.dart';
import '../../core/utils/logger.dart';

class StudentInfo extends StatefulWidget {
  const StudentInfo({Key? key}) : super(key: key);

  @override
  _StudentInfoState createState() => _StudentInfoState();
}

class _StudentInfoState extends State<StudentInfo> {
  Map<String, dynamic>? student;
  bool isLoading = true;
  bool isError = false;
  String errorMessage = '';

  // Use global token service
  final TokenService _tokenService = TokenService();
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();
    AppLogger.info('🚀 StudentInfo: Initializing student info page');
    print('🚀 DEBUG: StudentInfo initState called');
    fetchStudentDetails();
  }

  /// Refresh student data
  Future<void> refreshStudentData() async {
    AppLogger.info('🔄 StudentInfo: Manual refresh triggered');
    print('🔄 DEBUG: refreshStudentData called');
    await fetchStudentDetails();
  }

  /// Validate session and refresh token if needed
  Future<bool> validateAndRefreshSession() async {
    try {
      AppLogger.info('🔑 StudentInfo: Starting session validation and token refresh...');
      print('🔑 DEBUG: validateAndRefreshSession called');

      // Check current token status before refresh
      final currentToken = await _tokenService.getToken();
      print('🔑 DEBUG: Current token exists: ${currentToken != null}');
      print('🔑 DEBUG: Current token length: ${currentToken?.length ?? 0}');

      if (currentToken != null) {
        final isCurrentValid = await _tokenService.isTokenValid();
        print('🔑 DEBUG: Current token is valid: $isCurrentValid');
        AppLogger.info('🔑 StudentInfo: Current token valid: $isCurrentValid');
      }

      // Use API service to validate session and refresh token
      print('🔑 DEBUG: Calling API service validateSessionAndRefreshToken...');
      final data = await _apiService.validateSessionAndRefreshToken();
      print('🔑 DEBUG: API response received: ${data.toString()}');
      AppLogger.info('🔑 StudentInfo: API response keys: ${data.keys.toList()}');

      final newToken = data['token'] ?? data['access_token'] ?? data['jwt_token'];
      print('🔑 DEBUG: New token extracted: ${newToken != null}');
      print('🔑 DEBUG: New token length: ${newToken?.length ?? 0}');

      if (newToken != null) {
        // Store the new token using global token service
        print('🔑 DEBUG: Storing new token...');
        await _tokenService.storeToken(newToken);
        print('🔑 DEBUG: New token stored successfully');
        AppLogger.info('🔑 StudentInfo: Session validated and token refreshed successfully');

        // Verify the token was stored
        final storedToken = await _tokenService.getToken();
        print('🔑 DEBUG: Verification - stored token exists: ${storedToken != null}');
        print('🔑 DEBUG: Verification - tokens match: ${storedToken == newToken}');

        return true;
      } else {
        print('🔑 DEBUG: No token found in response data');
        AppLogger.warning('🔑 StudentInfo: No token received from validate-session endpoint');
        AppLogger.warning('🔑 StudentInfo: Response data: $data');
        return false;
      }
    } catch (e) {
      print('🔑 DEBUG: Exception in validateAndRefreshSession: $e');
      AppLogger.error('🔑 StudentInfo: Error validating session: $e');
      return false;
    }
  }

  /// Fetch student details from API using global services
  Future<void> fetchStudentDetails() async {
    AppLogger.info('📊 StudentInfo: Starting fetchStudentDetails...');
    print('📊 DEBUG: fetchStudentDetails called');

    setState(() {
      isLoading = true;
      isError = false;
    });
    print('📊 DEBUG: UI state set to loading');

    try {
      // Check if token exists first
      final currentToken = await _tokenService.getToken();
      print('📊 DEBUG: Token exists: ${currentToken != null}');
      print('📊 DEBUG: Token length: ${currentToken?.length ?? 0}');
      AppLogger.info('📊 StudentInfo: Token exists: ${currentToken != null}');

      if (currentToken != null) {
        // Show first few and last few characters of token for debugging (safely)
        final tokenPreview = currentToken.length > 20
            ? '${currentToken.substring(0, 10)}...${currentToken.substring(currentToken.length - 10)}'
            : currentToken;
        print('📊 DEBUG: Token preview: $tokenPreview');
      }

      // Check if token is valid first
      print('📊 DEBUG: Checking token validity...');
      final isValid = await _tokenService.isTokenValid();
      print('📊 DEBUG: Token is valid: $isValid');
      AppLogger.info('📊 StudentInfo: Token is valid: $isValid');

      if (!isValid) {
        print('📊 DEBUG: Token invalid, setting error state');
        setState(() {
          isError = true;
          errorMessage = 'Authentication token has expired. Please log in again.';
          isLoading = false;
        });
        AppLogger.warning('📊 StudentInfo: Authentication token is invalid or expired');
        return;
      }

      // Use API service to fetch student dashboard data
      print('📊 DEBUG: Calling API service fetchStudentDashboard...');
      AppLogger.info('📊 StudentInfo: Calling fetchStudentDashboard API...');

      final data = await _apiService.fetchStudentDashboard();
      print('📊 DEBUG: API call successful, response received');
      print('📊 DEBUG: Response keys: ${data.keys.toList()}');
      print('📊 DEBUG: Response data: ${data.toString()}');
      AppLogger.info('📊 StudentInfo: Successfully fetched student dashboard data');
      AppLogger.info('📊 StudentInfo: Response structure: ${data.keys.toList()}');

      // Check for student data in response
      final studentData = data['student'] ?? data;
      print('📊 DEBUG: Student data extracted: ${studentData != null}');
      if (studentData != null) {
        print('📊 DEBUG: Student data keys: ${(studentData as Map).keys.toList()}');
        AppLogger.info('📊 StudentInfo: Student data fields: ${(studentData as Map).keys.toList()}');
      }

      setState(() {
        student = studentData; // Handle different response structures
        isLoading = false;
      });
      print('📊 DEBUG: UI state updated with student data');
      AppLogger.info('📊 StudentInfo: UI updated with student information');

    } catch (e) {
      print('📊 DEBUG: Exception caught: $e');
      print('📊 DEBUG: Exception type: ${e.runtimeType}');
      AppLogger.error('📊 StudentInfo: Exception while fetching student dashboard: $e');

      String errorMessage = 'Unable to fetch student details. Please try again.';

      // Handle specific error types
      if (e.toString().contains('401') || e.toString().contains('Unauthorized')) {
        errorMessage = 'Session expired. Please log in again.';
        print('📊 DEBUG: 401/Unauthorized error detected');
      } else if (e.toString().contains('Network') || e.toString().contains('connection')) {
        errorMessage = 'Network error. Please check your connection and try again.';
        print('📊 DEBUG: Network error detected');
      } else if (e.toString().contains('timeout')) {
        errorMessage = 'Request timeout. Please try again.';
        print('📊 DEBUG: Timeout error detected');
      }

      print('📊 DEBUG: Setting error state with message: $errorMessage');
      setState(() {
        isError = true;
        this.errorMessage = errorMessage;
        isLoading = false;
      });
      AppLogger.error('📊 StudentInfo: Final error message: $errorMessage');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF10E7DC)),
              strokeWidth: 2,
            ),
            SizedBox(height: 16),
            Text(
              'Loading student information...',
              style: TextStyle(color: Colors.grey[600], fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (isError) {
      return Center(
        child: Container(
          margin: EdgeInsets.all(24),
          padding: EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Color(0xFFFEE6E6),
            border: Border.all(color: Color(0xFFFECACA)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.warning, color: Colors.red[500], size: 48),
              SizedBox(height: 16),
              Text(
                'Error Loading Student Information',
                style: TextStyle(
                  color: Colors.red[800],
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                ),
              ),
              SizedBox(height: 8),
              Text(
                errorMessage,
                style: TextStyle(color: Colors.red[600], fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      print('🔄 DEBUG: Retry button pressed');
                      AppLogger.info('🔄 StudentInfo: User pressed retry button');
                      refreshStudentData();
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF10E7DC),
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  OutlinedButton.icon(
                    onPressed: () async {
                      print('🔑 DEBUG: Refresh Session button pressed');
                      AppLogger.info('🔑 StudentInfo: User pressed refresh session button');

                      final success = await validateAndRefreshSession();
                      print('🔑 DEBUG: Session refresh result: $success');
                      AppLogger.info('🔑 StudentInfo: Session refresh success: $success');

                      if (success) {
                        print('🔑 DEBUG: Session refresh successful, calling refreshStudentData');
                        AppLogger.info('🔑 StudentInfo: Session refreshed, fetching student data');
                        refreshStudentData();
                      } else {
                        print('🔑 DEBUG: Session refresh failed, showing error snackbar');
                        AppLogger.warning('🔑 StudentInfo: Session refresh failed');
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Failed to refresh session. Please log in again.'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
                    icon: const Icon(Icons.key),
                    label: const Text('Refresh Session'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(0xFF10E7DC),
                      side: const BorderSide(color: Color(0xFF10E7DC)),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }

    if (student == null) {
      return Center(
        child: Container(
          margin: EdgeInsets.all(24),
          padding: EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Color(0xFFF7FAFC),
            border: Border.all(color: Color(0xFFE2E8F0)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.person, color: Colors.grey[400], size: 48),
              SizedBox(height: 16),
              Text(
                'No student information available',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Main student profile display with pull-to-refresh
    return RefreshIndicator(
      onRefresh: refreshStudentData,
      color: const Color(0xFF10E7DC),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
          CircleAvatar(
            radius: 40,
            child: Text('🎓', style: TextStyle(fontSize: 32)),
          ),
          SizedBox(height: 16),
          Text(
            '${student!['first_name']} ${student!['last_name']}',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(student!['student_email'] ?? 'N/A', style: TextStyle(fontSize: 16)),
          SizedBox(height: 24),
          Card(
            elevation: 4,
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildInfoRow('Student ID', student!['id']?.toString() ?? 'N/A'),
                  _buildInfoRow('Phone', student!['phone'] ?? 'N/A'),
                  _buildInfoRow('DOB', student!['dob'] != null
                      ? DateTime.parse(student!['dob']).toString().split(' ')[0]
                      : 'N/A'),
                  _buildInfoRow('Religion', student!['religion'] ?? 'N/A'),
                  _buildInfoRow('10th Marks', student!['marks_10th']?.toString() ?? 'N/A'),
                  _buildInfoRow('12th Marks', student!['marks_12th']?.toString() ?? 'N/A'),
                ],
              ),
            ),
          ),
        ],
      ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Text('$label: ', style: TextStyle(fontWeight: FontWeight.bold)),
          Flexible(child: Text(value)),
        ],
      ),
    );
  }
}
