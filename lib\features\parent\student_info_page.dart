import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // ✅ For date parsing
import '../../core/services/token_service.dart';
import '../../core/services/api_service.dart';
import '../../core/utils/logger.dart';

class StudentInfo extends StatefulWidget {
  const StudentInfo({Key? key}) : super(key: key);

  @override
  _StudentInfoState createState() => _StudentInfoState();
}

class _StudentInfoState extends State<StudentInfo> {
  Map<String, dynamic>? student;
  bool isLoading = true;
  bool isError = false;
  String errorMessage = '';

  final TokenService _tokenService = TokenService();
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();
    AppLogger.info('🚀 StudentInfo: Initializing student info page');
    fetchStudentDetails();
  }

  Future<void> refreshStudentData() async {
    AppLogger.info('StudentInfo: Manual refresh triggered');
    await fetchStudentDetails();
  }

  Future<bool> validateAndRefreshSession() async {
    try {
      AppLogger.info('StudentInfo: Starting session validation and token refresh...');
      final data = await _apiService.validateSessionAndRefreshToken();
      final newToken = data['token'] ?? data['access_token'] ?? data['jwt_token'];

      if (newToken != null) {
        await _tokenService.storeToken(newToken);
        return true;
      } else {
        return false;
      }
    } catch (e) {
      AppLogger.error('🔑 StudentInfo: Error validating session: $e');
      return false;
    }
  }

  Future<void> fetchStudentDetails() async {
    AppLogger.info('📊 StudentInfo: Starting fetchStudentDetails...');

    setState(() {
      isLoading = true;
      isError = false;
    });

    try {
      final currentToken = await _tokenService.getToken();

      if (currentToken == null || !(await _tokenService.isTokenValid())) {
        setState(() {
          isError = true;
          errorMessage = 'Authentication token has expired. Please log in again.';
          isLoading = false;
        });
        return;
      }

      final data = await _apiService.fetchParentDashboard();
      final studentData = data['student'] ?? data['students']?.first ?? data;

      setState(() {
        student = studentData;
        isLoading = false;
      });
    } catch (e) {
      String msg = 'Unable to fetch student details. Please try again.';
      if (e.toString().contains('401')) {
        msg = 'Session expired. Please log in again.';
      } else if (e.toString().contains('Network')) {
        msg = 'Network error. Please check your connection.';
      }
      setState(() {
        isError = true;
        errorMessage = msg;
        isLoading = false;
      });
    }
  }

  /// ✅ Date formatting helper
  String _formatDate(dynamic dob) {
    if (dob == null || dob.toString().isEmpty) {
      return 'N/A';
    }
    try {
      // Try backend RFC1123 format: "Wed, 09 Aug 2000 00:00:00 GMT"
      DateTime parsed = DateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", "en_US").parseUtc(dob);
      return DateFormat('dd-MM-yyyy').format(parsed.toLocal());
    } catch (_) {
      try {
        // Try ISO format: "2000-08-09"
        DateTime parsed = DateTime.parse(dob);
        return DateFormat('dd-MM-yyyy').format(parsed);
      } catch (_) {
        return 'Invalid Date';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 48,
              height: 48,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF10E7DC)),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Loading student information...',
              style: TextStyle(color: Colors.grey[600], fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      );
    }

    if (isError) {
      return Center(
        child: Container(
          margin: const EdgeInsets.all(24),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: const Color(0xFFFEE6E6),
            border: Border.all(color: const Color(0xFFFECACA)),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text("⚠️", style: TextStyle(fontSize: 40)),
              const SizedBox(height: 12),
              Text(
                'Error Loading Student Information',
                style: TextStyle(color: Colors.red[800], fontWeight: FontWeight.w600, fontSize: 18),
              ),
              const SizedBox(height: 6),
              Text(errorMessage, style: TextStyle(color: Colors.red[600], fontSize: 14), textAlign: TextAlign.center),
            ],
          ),
        ),
      );
    }

    if (student == null) {
      return Center(
        child: Container(
          margin: const EdgeInsets.all(24),
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: const Color(0xFFF7FAFC),
            border: Border.all(color: const Color(0xFFE2E8F0)),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: const [
              Text("👤", style: TextStyle(fontSize: 40, color: Colors.grey)),
              SizedBox(height: 12),
              Text("No student information available", style: TextStyle(color: Colors.grey, fontSize: 16)),
            ],
          ),
        ),
      );
    }

    // ✅ Main styled UI
    return RefreshIndicator(
      onRefresh: refreshStudentData,
      color: const Color(0xFF10E7DC),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Outer Card with background
            Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF10E7DC), Color(0xFF0DA5A2)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [BoxShadow(color: Colors.black26, blurRadius: 12, offset: Offset(0, 6))],
              ),
              child: Stack(
                children: [
                  Positioned(
                    top: -40,
                    right: -40,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: -30,
                    left: -30,
                    child: Container(
                      width: 90,
                      height: 90,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                  // Main content
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        // Header
                        Column(
                          children: [
                            Container(
                              width: 64,
                              height: 64,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                shape: BoxShape.circle,
                              ),
                              child: const Center(child: Text("🎓", style: TextStyle(fontSize: 28))),
                            ),
                            const SizedBox(height: 12),
                            const Text("Student Profile",
                                style: TextStyle(color: Colors.white, fontSize: 24, fontWeight: FontWeight.bold)),
                            Container(
                              margin: const EdgeInsets.symmetric(vertical: 8),
                              width: 60,
                              height: 4,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.5),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),

                        // Info Box
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.95),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 8)],
                          ),
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: [
                              // Personal Information
                              _sectionTitle("Personal Information"),
                              const SizedBox(height: 12),
                              _buildInfoItem("Full Name",
                                  "${student!['first_name'] ?? ''} ${student!['last_name'] ?? ''}"),
                              _buildInfoItem("Student ID", student!['id']?.toString() ?? "N/A"),
                              _buildInfoItem("Email Address", student!['student_email'] ?? "N/A"),
                              _buildInfoItem("Phone Number", student!['phone'] ?? "N/A"),
                              _buildInfoItem("Date of Birth", _formatDate(student!['dob'])),
                              _buildInfoItem("Religion", student!['religion'] ?? "N/A"),

                              const SizedBox(height: 24),

                              // Academic Records
                              _sectionTitle("Academic Records"),
                              const SizedBox(height: 12),
                              _buildMarksCard("10th Grade Marks", student!['marks_10th']?.toString() ?? "N/A", "📚"),
                              const SizedBox(height: 12),
                              _buildMarksCard("12th Grade Marks", student!['marks_12th']?.toString() ?? "N/A", "🎯"),

                              const SizedBox(height: 24),

                              // Quote
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.grey[50],
                                  border: const Border(
                                    left: BorderSide(color: Color(0xFF10E7DC), width: 4),
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Text(
                                  "“Trust your child’s journey, even if it doesn’t look like yours...”",
                                  style: TextStyle(
                                    fontStyle: FontStyle.italic,
                                    color: Colors.black87,
                                    fontSize: 13,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // ✅ Helper UI widgets
  Widget _sectionTitle(String title) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 7, right: 8),
            decoration: const BoxDecoration(color: Color(0xFF10E7DC), shape: BoxShape.circle),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label, style: const TextStyle(color: Colors.grey, fontSize: 12)),
                Text(value,
                    style: const TextStyle(color: Colors.black87, fontSize: 15, fontWeight: FontWeight.w500)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMarksCard(String title, String value, String emoji) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF10E7DC),
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(title, style: const TextStyle(color: Colors.white70, fontSize: 12)),
            Text(value, style: const TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold)),
          ]),
          Text(emoji, style: const TextStyle(fontSize: 28, color: Colors.white70)),
        ],
      ),
    );
  }
}
