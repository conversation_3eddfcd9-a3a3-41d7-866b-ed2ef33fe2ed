import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/services/auth_service.dart';
import '../../../../core/services/api_service.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/models/menu_models.dart';
import '../../../../core/models/auth_models.dart';
import '../../../../core/config/app_config.dart';
import '../widgets/role_based_drawer.dart';
import '../widgets/dashboard_header.dart';
import '../widgets/quick_actions_grid.dart';
import '../widgets/recent_activities.dart';
import '../../../student/privacy_policy/privacy_policy_page.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({Key? key}) : super(key: key);

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> with TickerProviderStateMixin {
  final AuthService _authService = AuthService();
  final ApiService _apiService = ApiService();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  
  List<MenuItem> _menuItems = [];
  bool _isLoadingMenu = true;
  String? _menuError;

  late AnimationController _fadeController;
  late AnimationController _slideController;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadMenuItems();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeController.forward();
    _slideController.forward();
  }

  Future<void> _loadMenuItems() async {
    try {
      final userRole = _authService.userRole;
      if (userRole == null) {
        AppLogger.error('User role is null, redirecting to login');
        context.go('/auth/login');
        return;
      }

      AppLogger.info('Loading menu items for role: $userRole');
      
      final menuItems = await _apiService.getMenuItems(userRole);
      
      if (mounted) {
        setState(() {
          _menuItems = menuItems;
          _isLoadingMenu = false;
        });
      }
      
      AppLogger.info('Menu items loaded successfully: ${menuItems.length} items');
    } catch (e) {
      AppLogger.error('Failed to load menu items: $e');
      
      if (mounted) {
        setState(() {
          _menuError = 'Failed to load menu items';
          _isLoadingMenu = false;
        });
      }
    }
  }

  Future<void> _handleLogout() async {
    final shouldLogout = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );

    if (shouldLogout == true) {
      AppLogger.userAction('Logout initiated from dashboard');
      await _authService.logout();
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = _authService.currentUser;
    
    if (user == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: AppTheme.backgroundColor,
      drawer: RoleBasedDrawer(
        menuItems: _menuItems,
        isLoading: _isLoadingMenu,
        error: _menuError,
        onRetry: _loadMenuItems,
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _loadMenuItems,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              children: [
                // Header Section
                AnimatedBuilder(
                  animation: _fadeController,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _fadeController.value,
                      child: DashboardHeader(
                        user: user,
                        onMenuTap: () => _scaffoldKey.currentState?.openDrawer(),
                        onLogout: _handleLogout,
                      ),
                    );
                  },
                ),
                
                // Main Content
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Welcome Section
                      _buildWelcomeSection(user),
                      
                      const SizedBox(height: 24),
                      
                      // Quick Actions
                      _buildQuickActionsSection(),
                      
                      const SizedBox(height: 24),
                      
                      // Recent Activities
                      _buildRecentActivitiesSection(),
                      
                      const SizedBox(height: 24),
                      
                      // Role-specific content
                      _buildRoleSpecificContent(user.role),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

Widget _buildWelcomeSection(UserData user) {
  return AnimatedBuilder(
    animation: _slideController,
    builder: (context, child) {
      return Column(
        children: [
          Transform.translate(
            offset: Offset(0, 30 * (1 - _slideController.value)),
            child: Opacity(
              opacity: _slideController.value,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor.withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back,',
                      style: AppTheme.bodyMedium.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      user.displayName,
                      style: AppTheme.headingMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        AppConfig.roleDisplayNames[user.role] ?? user.role.toUpperCase(),
                        style: AppTheme.bodySmall.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),

          // // Privacy Policy Button
          // ElevatedButton.icon(
          //   onPressed: () {
          //     Navigator.push(
          //       context,
          //       MaterialPageRoute(
          //         builder: (context) => const PrivacyPolicyPage(),
          //       ),
          //     );
          //   },
          //   icon: const Icon(Icons.privacy_tip),
          //   label: const Text('Privacy Policy'),
          //   style: ElevatedButton.styleFrom(
          //     backgroundColor: AppTheme.secondaryColor,
          //     foregroundColor: Colors.white,
          //     shape: RoundedRectangleBorder(
          //       borderRadius: BorderRadius.circular(12),
          //     ),
          //   ),
          // ),
        ],
      );
    },
  );
}

  Widget _buildQuickActionsSection() {
    return AnimatedBuilder(
      animation: _slideController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 40 * (1 - _slideController.value)),
          child: Opacity(
            opacity: _slideController.value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Quick Actions',
                  style: AppTheme.headingSmall.copyWith(
                    color: AppTheme.textPrimary,
                  ),
                ),
                const SizedBox(height: 16),
                QuickActionsGrid(
                  menuItems: _menuItems.take(6).toList(),
                  isLoading: _isLoadingMenu,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentActivitiesSection() {
    return AnimatedBuilder(
      animation: _slideController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - _slideController.value)),
          child: Opacity(
            opacity: _slideController.value * 0.8,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Recent Activities',
                  style: AppTheme.headingSmall.copyWith(
                    color: AppTheme.textPrimary,
                  ),
                ),
                const SizedBox(height: 16),
                const RecentActivities(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRoleSpecificContent(String role) {
    return AnimatedBuilder(
      animation: _slideController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 60 * (1 - _slideController.value)),
          child: Opacity(
            opacity: _slideController.value * 0.6,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppTheme.surfaceColor,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppTheme.borderColor),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${AppConfig.roleDisplayNames[role] ?? role} Dashboard',
                    style: AppTheme.headingSmall.copyWith(
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Role-specific features and content will be displayed here based on your permissions and access level.',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () {
                      // Navigate to role-specific features
                      AppLogger.userAction('Role-specific navigation', {'role': role});
                    },
                    icon: const Icon(Icons.explore),
                    label: const Text('Explore Features'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.secondaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
