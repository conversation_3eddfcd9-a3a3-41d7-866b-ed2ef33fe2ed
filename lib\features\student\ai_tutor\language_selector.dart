import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../ai_tutor/pdfviewer_page.dart'; // Update with correct path
import 'package:flutter/foundation.dart' show debugPrint;
import '../ai_tutor/slide_content_service.dart';
import '../ai_tutor/slide_content_service.dart'; // Import SlideContentPage

class LanguageSelector extends StatefulWidget {
  final String subjectName;
  final String subTopicId;
  final String subTopicName;

  const LanguageSelector({
    Key? key,
    required this.subjectName,
    required this.subTopicId,
    required this.subTopicName,
  }) : super(key: key);

  @override
  State<LanguageSelector> createState() {
    debugPrint('[LanguageSelector] Creating state for subject: ${subjectName}, '
        'subTopicId: ${subTopicId}, subTopicName: ${subTopicName}');
    return _LanguageSelectorState();
  }
}

class _LanguageSelectorState extends State<LanguageSelector> {
  final List<Map<String, dynamic>> languages = [
    {"id": "english", "name": "English", "color": Colors.blue},
    {"id": "thanglish", "name": "Tamil + English", "color": Colors.orange},
    {"id": "teluglish", "name": "Telugu + English", "color": Colors.green},
    {"id": "kanglish", "name": "Kannada + English", "color": Colors.purple},
    {"id": "manglish", "name": "Malayalam + English", "color": Colors.red},
    {"id": "hinglish", "name": "Hindi + English", "color": Colors.deepOrange},
  ];

  @override
  void initState() {
    super.initState();
    debugPrint('[LanguageSelectorState] Initializing with subject: ${widget.subjectName}, '
        'subTopicId: ${widget.subTopicId}, subTopicName: ${widget.subTopicName}');
  }

  Future<void> fetchLanguageContent(String selectedLanguageId) async {
    debugPrint('[LanguageSelectorState] Starting fetchLanguageContent with languageId: $selectedLanguageId');
    final url = "https://sasthra.in/api/selector-url/${widget.subTopicId}";
    debugPrint('[LanguageSelectorState] Fetching content from API → URL: $url, '
        'Language ID: $selectedLanguageId, Subject: ${widget.subjectName}');

    try {
      debugPrint('[LanguageSelectorState] Sending GET request to $url');
      final response = await http.get(Uri.parse(url));
      debugPrint('[LanguageSelectorState] Received response: Status Code: ${response.statusCode}, '
          'Body: ${response.body.length > 500 ? response.body.substring(0, 500) + '...' : response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        debugPrint('[LanguageSelectorState] Parsed response data: $data');
        final String processorSelectorUrl = data['process_selector_url'] as String;
        final String processorSelectorId = data['process_selector_id'].toString();

        debugPrint('[LanguageSelectorState] Extracted process_selector_url: $processorSelectorUrl, '
            'process_selector_id: $processorSelectorId');

        if (mounted) {
          debugPrint('[LanguageSelectorState] Navigating to PdfViewerPage with processorUrl: $processorSelectorUrl');
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) {
                debugPrint('[LanguageSelectorState] Building PdfViewerPage');
                return PdfViewerPage(
                  processorUrl: processorSelectorUrl, 
                  processSelectorId: processorSelectorId,
                    languageId: selectedLanguageId,
                    subjectName: widget.subjectName,
                );
              },
            ),
          );
          debugPrint('[LanguageSelectorState] Returned from PdfViewerPage');

          debugPrint('[LanguageSelectorState] Preparing to navigate to SlideContentPage with '
              'ID: $processorSelectorId, Language: $selectedLanguageId, Subject: ${widget.subjectName}');
        } else {
          debugPrint('[LanguageSelectorState] Widget not mounted, skipping PdfViewerPage navigation');
        }
      } else {
        debugPrint('[LanguageSelectorState] Failed to fetch language content: '
            'Status Code: ${response.statusCode}, Body: ${response.body}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text("Failed to load content: ${response.statusCode}"),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('[LanguageSelectorState] Exception fetching content: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Error loading content: $e"),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
    debugPrint('[LanguageSelectorState] Completed fetchLanguageContent');
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('[LanguageSelectorState] Building widget');
    return Scaffold(
      appBar: AppBar(
        title: Text("Select Language - ${widget.subjectName}"),
        backgroundColor: Colors.blue.shade400,
      ),
      body: ListView.builder(
        itemCount: languages.length,
        itemBuilder: (context, index) {
          final lang = languages[index];
          debugPrint('[LanguageSelectorState] Building ListTile for language: ${lang["name"]}');
          return Card(
            margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
            color: lang["color"],
            child: ListTile(
              title: Text(
                lang["id"],
                style: const TextStyle(color: Colors.white, fontSize: 16),
              ),
              trailing: const Icon(Icons.arrow_forward, color: Colors.white),
              onTap: () {
                debugPrint('[LanguageSelectorState] Language selected: ${lang["name"]} (ID: ${lang["id"]})');
                fetchLanguageContent(lang["id"]);
              },
            ),
          );
        },
      ),
    );
  }
}