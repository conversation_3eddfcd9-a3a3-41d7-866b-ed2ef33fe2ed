// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:pdfx/pdfx.dart';
// import 'package:http/http.dart' as http;
// import 'tts_service.dart';
// import 'slide_content_service.dart';

// class PdfViewerPage extends StatefulWidget {
//   final String processorUrl; // 🔹 pass URL dynamically

//   const PdfViewerPage({Key? key, required this.processorUrl}) : super(key: key);

//   @override
//   State<PdfViewerPage> createState() => _PdfViewerPageState();
// }

// class _PdfViewerPageState extends State<PdfViewerPage> {
//   PdfController? _pdfController;
//   final TTSService _ttsService = TTSService();
//   final SlideRepository _repository = SlideRepository();

//   int _totalPages = 0;
//   int _currentPage = 1;
//   bool _isFullScreen = false;
//   bool _isPdfLoading = true;
//   Map<String, String> _slideTranslations = {};

//   @override
//   void initState() {
//     super.initState();

//     // Force landscape
//     SystemChrome.setPreferredOrientations([
//       DeviceOrientation.landscapeLeft,
//       DeviceOrientation.landscapeRight,
//     ]);

//     _loadPdf(); // 🔹 Load PDF from processorUrl
//     _fetchSlideTranslations();
//   }

//   Future<void> _loadPdf() async {
//     try {
//       print("Fetching PDF from: ${widget.processorUrl}");
//       final response = await http.get(Uri.parse(widget.processorUrl));

//       if (response.statusCode == 200) {
//         print("PDF fetched successfully ");

//         final doc = await PdfDocument.openData(response.bodyBytes);

//         setState(() {
//           _pdfController = PdfController(
//             document: Future.value(doc),
//             initialPage: 1,
//           );
//           _totalPages = doc.pagesCount;
//           _isPdfLoading = false;
//         });
//       } else {
//         print("Failed to load PDF ❌: ${response.statusCode}");
//       }
//     } catch (e) {
//       print("Error loading PDF ❌: $e");
//     }
//   }

//   Future<void> _fetchSlideTranslations() async {
//     final translations = await _repository.fetchTranslations();
//     setState(() => _slideTranslations = translations);
//     _updateCaption();
//   }

//   void _updateCaption() {
//     String key = "slide $_currentPage";
//     String text = _slideTranslations[key] ?? "No caption for page $_currentPage";

//     if (_ttsService.isPlaying) {
//       _ttsService.play(text, _goToNextPage);
//     }
//   }

//   void _togglePlayPause() {
//     if (_ttsService.isPlaying) {
//       _ttsService.pause();
//       setState(() {});
//     } else {
//       String key = "slide $_currentPage";
//       String text = _slideTranslations[key] ?? "";
//       if (text.isNotEmpty) {
//         _ttsService.play(text, _goToNextPage).then((_) => setState(() {}));
//       }
//     }
//   }

//   void _goToNextPage() {
//     if (_currentPage < _totalPages) {
//       _pdfController?.jumpToPage(_currentPage + 1);
//       setState(() => _currentPage++);
//       _updateCaption();
//     } else {
//       print("End of slides ✅");
//     }
//   }

//   void _toggleFullScreen() {
//     setState(() => _isFullScreen = !_isFullScreen);

//     if (_isFullScreen) {
//       SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
//     } else {
//       SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
//     }
//   }

//   @override
//   void dispose() {
//     _ttsService.dispose();
//     SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: _isFullScreen
//           ? null
//           : AppBar(
//               title: Text("PDF Viewer ($_currentPage / $_totalPages)"),
//               actions: [
//                 IconButton(
//                   icon: const Icon(Icons.arrow_back),
//                   onPressed: () {
//                     if (_currentPage > 1) {
//                       _pdfController?.jumpToPage(_currentPage - 1);
//                     }
//                   },
//                 ),
//                 IconButton(
//                   icon: const Icon(Icons.arrow_forward),
//                   onPressed: () {
//                     if (_currentPage < _totalPages) {
//                       _pdfController?.jumpToPage(_currentPage + 1);
//                     }
//                   },
//                 ),
//                 IconButton(
//                   icon: Icon(_isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen),
//                   onPressed: _toggleFullScreen,
//                 ),
//               ],
//             ),
//       body: Stack(
//         children: [
//           if (_isPdfLoading || _pdfController == null)
//             const Center(child: CircularProgressIndicator())
//           else
//             PdfView(
//               controller: _pdfController!,
//               scrollDirection: Axis.horizontal,
//               onPageChanged: (page) {
//                 setState(() => _currentPage = page);
//                 _updateCaption();
//               },
//             ),
//           if (_ttsService.isLoading)
//             Container(
//               color: Colors.black54,
//               child: const Center(
//                 child: CircularProgressIndicator(color: Colors.white),
//               ),
//             ),
//         ],
//       ),
//       floatingActionButton: FloatingActionButton(
//         onPressed: _togglePlayPause,
//         child: Icon(_ttsService.isPlaying ? Icons.pause : Icons.play_arrow),
//       ),
//     );
//   }
// }


import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pdfx/pdfx.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart' show debugPrint;
import 'dart:convert';
import '../../../core/config/app_config.dart';
import 'tts_service.dart';

class PdfViewerPage extends StatefulWidget {
  final String processorUrl; // URL to the PDF
  final String subjectName; // Subject for fetching slide content
  final String processSelectorId; // For explain_slides API
  final String languageId; // For explain_slides API

  const PdfViewerPage({
    Key? key,
    required this.processorUrl,
    required this.subjectName,
    required this.processSelectorId,
    required this.languageId,
  }) : super(key: key);

  @override
  State<PdfViewerPage> createState() => _PdfViewerPageState();
}

class _PdfViewerPageState extends State<PdfViewerPage> {
  static const String baseUrl = "https://testing.sasthra.in";
  PdfController? _pdfController;
  int _totalPages = 0;
  int _currentPage = 1; // Initial page set to 1
  bool _isFullScreen = false;
  bool _isPdfLoading = true;
  Map<String, String> _slideTranslations = {}; // Store slide translations as a map
  String? _error;
  late TTSService _ttsService; // TTS service instance
  String? _currentlyPlayingSlideKey; // Track which slide is playing

  @override
  void initState() {
    super.initState();
    _ttsService = TTSService();
    debugPrint('[PdfViewerPageState] Initialized with processorUrl: ${widget.processorUrl}, '
        'subject: ${widget.subjectName}, processSelectorId: ${widget.processSelectorId}, '
        'languageId: ${widget.languageId}, initialPage: $_currentPage');

    // Force landscape orientation
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    _loadPdf();
    _fetchSlideContent();
  }

  Future<void> _loadPdf() async {
    debugPrint('[PdfViewerPageState] Loading PDF from: ${widget.processorUrl}');
    try {
      final response = await http.get(Uri.parse(widget.processorUrl));
      debugPrint('[PdfViewerPageState] PDF fetch response: Status Code: ${response.statusCode}, '
          'Content-Length: ${response.contentLength}');

      if (response.statusCode == 200) {
        debugPrint('[PdfViewerPageState] PDF fetched successfully');
        final doc = await PdfDocument.openData(response.bodyBytes);

        setState(() {
          _pdfController = PdfController(
            document: Future.value(doc),
            initialPage: 1, // Ensure initial page is 1
          );
          _totalPages = doc.pagesCount;
          _isPdfLoading = false;
          debugPrint('[PdfViewerPageState] PDF loaded with $_totalPages pages, currentPage: $_currentPage');
        });
      } else {
        debugPrint('[PdfViewerPageState] Failed to load PDF: ${response.statusCode} - ${response.body}');
        setState(() {
          _isPdfLoading = false;
          _error = "Failed to load PDF: ${response.statusCode}";
        });
      }
    } catch (e) {
      debugPrint('[PdfViewerPageState] Error loading PDF: $e');
      setState(() {
        _isPdfLoading = false;
        _error = "Error loading PDF: $e";
      });
    }
  }

  String _getEndpoint(String subject) {
    debugPrint('[PdfViewerPageState] Fetching endpoint for subject: $subject');
    final endpoints = {
      "Mathematics": "$baseUrl/classroom/mathapi/explain_slides",
      "Chemistry": "$baseUrl/classroom/chemapi/explain_slides",
      "Physics": "$baseUrl/classroom/physicsapi/explain_slides",
      "Biology": "$baseUrl/classroom/bioapi/explain_slides"
    };
    final endpoint = endpoints[subject] ?? "";
    debugPrint('[PdfViewerPageState] Selected endpoint: $endpoint');
    return endpoint;
  }

  Future<void> _fetchSlideContent() async {
    debugPrint('[PdfViewerPageState] Fetching slide content for subject: ${widget.subjectName}, '
        'processSelectorId: ${widget.processSelectorId}, languageId: ${widget.languageId}');
    try {
      final endpoint = _getEndpoint(widget.subjectName);
      if (endpoint.isEmpty) {
        debugPrint('[PdfViewerPageState] Invalid subject: ${widget.subjectName}');
        throw Exception("Invalid subject: ${widget.subjectName}");
      }

      final requestBody = jsonEncode({
        "process_selector_id": widget.processSelectorId,
        "language": widget.languageId,
      });
      debugPrint('[PdfViewerPageState] Sending POST request to $endpoint with body: $requestBody');
      debugPrint('[PdfViewerPageState] Request headers: ${{
        "Content-Type": "application/json",
        ...AppConfig.defaultHeaders,
      }}');

      final response = await http.post(
        Uri.parse(endpoint),
        headers: {
          "Content-Type": "application/json",
          ...AppConfig.defaultHeaders,
        },
        body: requestBody,
      );

      debugPrint('[PdfViewerPageState] Received response with status code: ${response.statusCode}, '
          'Body: ${response.body.length > 500 ? response.body.substring(0, 500) + '...' : response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        debugPrint('[PdfViewerPageState] Parsed slide content data: $data');
        
        // Convert the response to a proper map structure
        final Map<String, String> slideTranslations = {};
        data.forEach((key, value) {
          String content;
          if (value is Map && value['content'] != null) {
            content = value['content'].toString();
          } else {
            content = value.toString();
          }
          slideTranslations[key] = content;
          debugPrint('[PdfViewerPageState] Slide $key: ${content.length > 100 ? content.substring(0, 100) + '...' : content}');
        });

        setState(() {
          _slideTranslations = slideTranslations;
          debugPrint('[PdfViewerPageState] Slide translations set successfully, keys: ${slideTranslations.keys.toList()}');
        });
      } else {
        debugPrint('[PdfViewerPageState] Failed to fetch slide content: Status Code: ${response.statusCode}, '
            'Body: ${response.body}');
        setState(() {
          _error = "Failed to fetch slide content: ${response.statusCode}";
        });
      }
    } catch (e) {
      debugPrint('[PdfViewerPageState] Error fetching slide content: $e');
      setState(() {
        _error = "Error fetching slide content: $e";
      });
    }
  }

  Future<void> _playSlideContent(String content, String slideKey) async {
    debugPrint('[PdfViewerPageState] Playing TTS for slide: $slideKey, content: ${content.length > 100 ? content.substring(0, 100) + '...' : content}');
    if (_ttsService.isPlaying && _currentlyPlayingSlideKey == slideKey) {
      await _ttsService.pause();
      setState(() {
        _currentlyPlayingSlideKey = null;
        debugPrint('[PdfViewerPageState] Paused TTS for slide: $slideKey');
      });
    } else {
      await _ttsService.play(content, () {
        setState(() {
          _currentlyPlayingSlideKey = null;
          debugPrint('[PdfViewerPageState] TTS completed for slide: $slideKey');
        });
      });
      setState(() {
        _currentlyPlayingSlideKey = slideKey;
        debugPrint('[PdfViewerPageState] Started TTS for slide: $slideKey');
      });
    }
  }

  Future<void> _stopSlideContent() async {
    await _ttsService.stop();
    setState(() {
      _currentlyPlayingSlideKey = null;
      debugPrint('[PdfViewerPageState] Stopped TTS');
    });
  }

  @override
  void dispose() {
    debugPrint('[PdfViewerPageState] Disposing PdfViewerPage');
    _pdfController?.dispose();
    _ttsService.dispose();
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('[PdfViewerPageState] Building widget, currentPage: $_currentPage, totalPages: $_totalPages');
    
    // Use the correct mapping logic as requested
    String slideKey = "slide $_currentPage";
    String caption = _slideTranslations[slideKey] ?? "No caption for page $_currentPage";
    
    debugPrint('[PdfViewerPageState] Mapping Page $_currentPage to Slide Key: $slideKey, '
        'Caption: ${caption.length > 100 ? caption.substring(0, 100) + '...' : caption}');

    return WillPopScope(
      onWillPop: () async {
        debugPrint('[PdfViewerPageState] Back button pressed, navigating to language selector page');
        Navigator.pushReplacementNamed(context, '/language_selector');
        return false; // Prevent default pop behavior
      },
      child: Scaffold(
        appBar: _isFullScreen
            ? null
            : AppBar(
                title: Text("PDF Viewer ($_currentPage / $_totalPages)"),
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back_ios),
                  onPressed: () {
                    debugPrint('[PdfViewerPageState] AppBar back button pressed, navigating to language selector page');
                    Navigator.pushReplacementNamed(context, '/language_selector');
                  },
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: () {
                      if (_currentPage > 1) {
                        _pdfController?.jumpToPage(_currentPage - 1);
                        setState(() {
                          _currentPage--;
                          _stopSlideContent();
                          debugPrint('[PdfViewerPageState] Navigated to previous page: $_currentPage');
                        });
                      }
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.arrow_forward),
                    onPressed: () {
                      if (_currentPage < _totalPages) {
                        _pdfController?.jumpToPage(_currentPage + 1);
                        setState(() {
                          _currentPage++;
                          _stopSlideContent();
                          debugPrint('[PdfViewerPageState] Navigated to next page: $_currentPage');
                        });
                      }
                    },
                  ),
                  IconButton(
                    icon: Icon(_isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen),
                    onPressed: () {
                      setState(() {
                        _isFullScreen = !_isFullScreen;
                        debugPrint('[PdfViewerPageState] Toggling fullscreen: ${_isFullScreen ? 'enabled' : 'disabled'}');
                        SystemChrome.setEnabledSystemUIMode(
                          _isFullScreen ? SystemUiMode.immersive : SystemUiMode.edgeToEdge,
                        );
                      });
                    },
                  ),
                  IconButton(
                    icon: Icon(
                      _ttsService.isPlaying && _currentlyPlayingSlideKey == slideKey
                          ? Icons.pause
                          : Icons.play_arrow,
                      color: _ttsService.isLoading && _currentlyPlayingSlideKey == slideKey
                          ? Colors.grey
                          : Colors.blue,
                      semanticLabel: _ttsService.isPlaying && _currentlyPlayingSlideKey == slideKey
                          ? 'Pause'
                          : 'Play',
                    ),
                    onPressed: _ttsService.isLoading && _currentlyPlayingSlideKey == slideKey
                        ? null
                        : () {
                            if (caption != "No caption for page $_currentPage") {
                              _playSlideContent(caption, slideKey);
                            } else {
                              debugPrint('[PdfViewerPageState] Cannot play TTS: No slide content for page $_currentPage (key: $slideKey)');
                            }
                          },
                  ),
                  IconButton(
                    icon: const Icon(
                      Icons.stop,
                      color: Colors.red,
                      semanticLabel: 'Stop',
                    ),
                    onPressed: _currentlyPlayingSlideKey == slideKey ? _stopSlideContent : null,
                  ),
                ],
              ),
        body: _error != null
            ? Center(child: Text(_error!, style: const TextStyle(color: Colors.red)))
            : _isPdfLoading || _pdfController == null
                ? const Center(child: CircularProgressIndicator())
                : PdfView(
                    controller: _pdfController!,
                    scrollDirection: Axis.horizontal,
                    onPageChanged: (page) {
                      setState(() {
                        _currentPage = page;
                        _stopSlideContent();
                        debugPrint('[PdfViewerPageState] Page changed to: $_currentPage');
                      });
                    },
                  ),
      ),
    );
  }
}