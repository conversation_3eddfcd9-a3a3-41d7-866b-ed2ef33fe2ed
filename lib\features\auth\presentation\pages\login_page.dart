/**
 * login_page.dart - User Authentication Login Page
 *
 * English: This page handles the initial user authentication for the Sasthra application.
 * It provides a secure login interface where users enter their credentials (username/password)
 * to access the application. The page includes form validation, error handling, loading states,
 * and smooth animations for enhanced user experience. After successful credential verification,
 * users are redirected to OTP verification.
 *
 * Tanglish: Inga user authentication ku login page irukku. Username password enter pannitu
 * app access panna vendiya page. Form validation, error handling, loading states - ellam
 * irukku. Credentials correct aana OTP verification ku redirect aagum. Smooth animations
 * um irukku better user experience ku.
 *
 * Key Features:
 * - Secure credential input with validation
 * - Password visibility toggle for user convenience
 * - Real-time form validation with error messages
 * - Loading states during authentication process
 * - Smooth animations and transitions
 * - Error handling with user-friendly messages
 * - Responsive design for different screen sizes
 *
 * Authentication Flow:
 * 1. User enters username and password
 * 2. Form validation checks input validity
 * 3. Credentials sent to authentication service
 * 4. On success: Navigate to OTP verification
 * 5. On failure: Display error message
 *
 * UI Components:
 * - Animated input fields with focus management
 * - Gradient login button with loading indicator
 * - Error message widget for validation feedback
 * - Password visibility toggle icon
 */

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/services/auth_service.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/logger.dart';
import '../widgets/animated_input_field.dart';
import '../widgets/gradient_button.dart';
import '../widgets/error_message_widget.dart';
import '../../../../core/models/auth_models.dart';

/**
 * LoginPage - User Authentication Interface Widget
 *
 * English: StatefulWidget that provides the login interface with form handling and animations.
 * Tanglish: Login interface provide panna vendiya StatefulWidget with form handling.
 */
class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _usernameFocus = FocusNode();
  final _passwordFocus = FocusNode();
  
  final AuthService _authService = AuthService();
  
  bool _isPasswordVisible = false;
  bool _isLoading = false;
  String? _errorMessage;

  late AnimationController _slideController;
  late AnimationController _fadeController;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _listenToAuthService();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    // Start animations
    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _fadeController.forward();
    });
  }

  void _listenToAuthService() {
    _authService.addListener(() {
      if (mounted) {
        final state = _authService.state;
        
        setState(() {
          _isLoading = state == AuthState.loading;
          _errorMessage = _authService.errorMessage;
        });
        
        if (state == AuthState.otpRequired) {
          context.go('/auth/otp');
        } else if (state == AuthState.authenticated) {
          context.go('/dashboard');
        }
      }
    });
  }

 Future<void> _handleLogin() async {
  if (!_formKey.currentState!.validate()) return;

  setState(() {
    _errorMessage = null;
  });

  HapticFeedback.lightImpact();

  final username = _usernameController.text.trim();
  final password = _passwordController.text;

  AppLogger.userAction('Login attempt', {'username': username});

  // 🔹 Test credentials shortcut
  if (username == "TESTUSER" && password == "test001") {
    AppLogger.info("Test credentials used. Skipping OTP flow.");

    _authService.setAuthenticatedUser(
      UserData(
        id: "test_id",
        username: username,
        role: "student",
        //displayName: "Test User",
      ),
    );

    if (mounted) {
      context.go('/dashboard');
    }
    return;
  }

  // Normal login
  final result = await _authService.login(username, password);

  if (!result.success && result.message != null) {
    setState(() {
      _errorMessage = result.message;
    });
    HapticFeedback.heavyImpact();
  }
}


  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _usernameFocus.dispose();
    _passwordFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              const SizedBox(height: 40),
              
              // Header Section
              _buildHeader(),
              
              const SizedBox(height: 60),
              
              // Login Form
              _buildLoginForm(),
              
              const SizedBox(height: 40),
              
              // Footer
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return AnimatedBuilder(
      animation: _slideController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - _slideController.value)),
          child: Opacity(
            opacity: _slideController.value,
            child: Column(
              children: [
                // Logo
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryColor.withOpacity(0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.school,
                    size: 40,
                    color: Colors.white,
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Welcome Text
                Text(
                  'Welcome Back!',
                  style: AppTheme.headingLarge.copyWith(
                    color: AppTheme.textPrimary,
                  ),
                ),
                
                const SizedBox(height: 8),
                
                Text(
                  'Sign in to continue your learning journey',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoginForm() {
    return AnimatedBuilder(
      animation: _fadeController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - _fadeController.value)),
          child: Opacity(
            opacity: _fadeController.value,
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  // Error Message
                  if (_errorMessage != null)
                    ErrorMessageWidget(
                      message: _errorMessage!,
                      onDismiss: () => setState(() => _errorMessage = null),
                    ).animate().fadeIn().slideY(begin: -0.3),
                  
                  const SizedBox(height: 16),
                  
                  // Username Field
                  AnimatedInputField(
                    controller: _usernameController,
                    focusNode: _usernameFocus,
                    label: 'Username',
                    hint: 'Enter your username',
                    prefixIconData: Icons.person_outline,
                    textInputAction: TextInputAction.next,
                    onFieldSubmitted: (_) => _passwordFocus.requestFocus(),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Username is required';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Password Field
                  AnimatedInputField(
                    controller: _passwordController,
                    focusNode: _passwordFocus,
                    label: 'Password',
                    hint: 'Enter your password',
                    prefixIconData: Icons.lock_outline,
                    obscureText: !_isPasswordVisible,
                    textInputAction: TextInputAction.done,
                    onFieldSubmitted: (_) => _handleLogin(),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                        color: AppTheme.textSecondary,
                      ),
                      onPressed: () {
                        setState(() {
                          _isPasswordVisible = !_isPasswordVisible;
                        });
                      },
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Password is required';
                      }
                      if (value.length < 6) {
                        return 'Password must be at least 6 characters';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Login Button
                  GradientButton(
                    onPressed: _isLoading ? null : _handleLogin,
                    isLoading: _isLoading,
                    child: const Text(
                      'Sign In',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFooter() {
    return AnimatedBuilder(
      animation: _fadeController,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeController.value * 0.7,
          child: Column(
            children: [
              Text(
                'Need help? Contact support',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.textTertiary,
                ),
              ),
              
              const SizedBox(height: 16),
              
              Text(
                '© 2024 Sasthra. All rights reserved.',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.textTertiary,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
