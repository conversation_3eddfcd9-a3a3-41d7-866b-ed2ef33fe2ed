import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'language_selector.dart';
import 'dart:math' as math;

class ParticlePainter extends CustomPainter {
  final double progress;

  ParticlePainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.shade200.withOpacity(0.5)
      ..style = PaintingStyle.fill;

    final random = math.Random();
    for (int i = 0; i < 50; i++) {
      double x = random.nextDouble() * size.width;
      double y = random.nextDouble() * size.height;
      double radius = random.nextDouble() * 2 + 1;
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class SubTopicsPage extends StatelessWidget {
  final String topicId;
  final String topicName;
  final String subjectName;

  const SubTopicsPage({
    Key? key,
    required this.topicId,
    required this.topicName,
    required this.subjectName,
  }) : super(key: key);

  Future<List<dynamic>> fetchSubTopics() async {
    final url = "https://sasthra.in/api/subtopics/$topicId";
    debugPrint("📡 Fetching subtopics from: $url");

    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data is List) {
          debugPrint("✅ SubTopics fetched successfully (${data.length})");
          return data;
        } else {
          debugPrint("❌ Invalid data format: Expected a list");
          return [];
        }
      } else {
        debugPrint("❌ Failed to load subtopics: ${response.body}");
        return [];
      }
    } catch (e) {
      debugPrint("⚠️ Exception fetching subtopics: $e");
      return [];
    }
  }

  Widget buildSubTopicCard(BuildContext context, Map<String, dynamic> subTopic, int index) {
    final subTopicId = subTopic["sub_topic_id"].toString();
    final subTopicName = subTopic["sub_topic_name"] ?? "Unnamed SubTopic";

    return TweenAnimationBuilder(
      duration: Duration(milliseconds: 400 + (index * 100)),
      curve: Curves.easeOutBack,
      tween: Tween<double>(begin: 0, end: 1),
      builder: (context, value, child) {
        final clampedValue = value.clamp(0.0, 1.0); // Ensure opacity is within 0.0 to 1.0
        return Opacity(
          opacity: clampedValue,
          child: Transform.scale(
            scale: clampedValue,
            child: child,
          ),
        );
      },
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue, Colors.blue.shade800],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.5),
                spreadRadius: 5,
                blurRadius: 15,
                offset: const Offset(0, 0), // Glow effect
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.book,
                  size: 25,
                  color: Colors.blue.shade200,
                ),
                const SizedBox(width: 5),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        subTopicName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 5),
                ElevatedButton(
                  onPressed: () {
                    debugPrint("➡️ Navigating to LanguageSelector: $subTopicName");
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LanguageSelector(
                          subjectName: subjectName,
                          subTopicId: subTopicId,
                          subTopicName: subTopicName,
                        ),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    shape: const CircleBorder(),
                    padding: const EdgeInsets.all(8),
                    backgroundColor: Colors.white,
                  ),
                  child: const Icon(Icons.arrow_forward_ios, size: 12, color: Colors.blue),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          CustomPaint(
            painter: ParticlePainter(1.0),
            child: Container(),
          ),
          FutureBuilder<List<dynamic>>(
            future: fetchSubTopics(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: CircularProgressIndicator(
                    color: Colors.blue,
                  ),
                );
              }
              if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
                return const Center(
                  child: Text(
                    "No subtopics found",
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.black54),
                  ),
                );
              }

              final subTopics = snapshot.data!;
              return Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const SizedBox(height: 20), // Move the text down slightly
                          Text(
                            topicName.toUpperCase(),
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              letterSpacing: 1,
                              fontSize: 24,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(8.0),
                        itemCount: subTopics.length,
                        itemBuilder: (context, index) => buildSubTopicCard(context, subTopics[index], index),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}