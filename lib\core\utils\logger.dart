/**
 * logger.dart - Application Logging Utility
 *
 * English: This utility provides a centralized logging system for the Sasthra application.
 * It offers different log levels (debug, info, warning, error) with pretty formatting,
 * colors, emojis, and timestamps. The logger helps in debugging, monitoring application
 * behavior, and tracking errors during development and production phases.
 *
 * Tanglish: Inga naama app ku centralized logging system irukku. Debug, info, warning,
 * error - different log levels irukku. Pretty formatting, colors, emojis, timestamps -
 * ellam irukku. Development and production la debugging and monitoring ku use pannum.
 *
 * Key Features:
 * - Multiple log levels with appropriate filtering
 * - Pretty formatted output with colors and emojis
 * - Timestamp inclusion for chronological tracking
 * - Stack trace support for error debugging
 * - Environment-based log level configuration
 * - Specialized logging methods for different contexts
 * - Performance monitoring capabilities
 *
 * Log Levels:
 * - Debug: Detailed information for development
 * - Info: General application information
 * - Warning: Potential issues that need attention
 * - Error: Error conditions that need immediate attention
 *
 * Specialized Loggers:
 * - Authentication: Login, logout, token operations
 * - API: HTTP requests and responses
 * - Navigation: Route changes and navigation events
 * - Performance: Timing and performance metrics
 *
 * Usage: AppLogger.info('Message'), AppLogger.error('Error', exception)
 */

import 'package:logger/logger.dart';
import '../config/app_config.dart';

/**
 * AppLogger - Centralized Logging Utility Class
 *
 * English: Static class that provides logging functionality with different levels and formatting.
 * Tanglish: Different log levels and formatting oda logging provide panna vendiya static class.
 */
class AppLogger {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
    level: AppConfig.isDebug ? Level.debug : Level.info,
  );

  /// Log debug message
  static void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    if (AppConfig.isDebug) {
      _logger.d(message, error: error, stackTrace: stackTrace);
    }
  }

  /// Log info message
  static void info(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log warning message
  static void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Log error message
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Log fatal error message
  static void fatal(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }

  /// Log API request
  static void apiRequest(String method, String url, [Map<String, dynamic>? data]) {
    if (AppConfig.isDebug) {
      debug('API Request: $method $url${data != null ? ' - Data: $data' : ''}');
    }
  }

  /// Log API response
  static void apiResponse(String method, String url, int statusCode, [dynamic data]) {
    if (AppConfig.isDebug) {
      debug('API Response: $method $url - Status: $statusCode${data != null ? ' - Data: $data' : ''}');
    }
  }

  /// Log authentication events
  static void auth(String event, [String? details]) {
    info('Auth: $event${details != null ? ' - $details' : ''}');
  }

  /// Log navigation events
  static void navigation(String from, String to) {
    debug('Navigation: $from -> $to');
  }

  /// Log storage operations
  static void storage(String operation, String key, [bool success = true]) {
    debug('Storage: $operation $key - ${success ? 'Success' : 'Failed'}');
  }

  /// Log token operations
  static void token(String operation, [String? details]) {
    info('Token: $operation${details != null ? ' - $details' : ''}');
  }

  /// Log performance metrics
  static void performance(String operation, Duration duration) {
    debug('Performance: $operation took ${duration.inMilliseconds}ms');
  }

  /// Log user actions
  static void userAction(String action, [Map<String, dynamic>? context]) {
    info('User Action: $action${context != null ? ' - Context: $context' : ''}');
  }

  /// Log cache operations
  static void cache(String operation, String key, [bool hit = true]) {
    debug('Cache: $operation $key - ${hit ? 'Hit' : 'Miss'}');
  }

  /// Log background tasks
  static void backgroundTask(String task, [String? status]) {
    info('Background Task: $task${status != null ? ' - $status' : ''}');
  }
}
