// plugins {
//     id 'com.android.application'
//     id 'kotlin-android'
//     id 'dev.flutter.flutter-gradle-plugin'
// }

// android {
//     namespace 'com.sasthra.app'
//     compileSdkVersion flutter.compileSdkVersion
//     ndkVersion "27.0.12077973"

//     compileOptions {
//         sourceCompatibility JavaVersion.VERSION_11
//         targetCompatibility JavaVersion.VERSION_11
//     }

//     kotlinOptions {
//         jvmTarget = JavaVersion.VERSION_11.toString()
//     }

//     defaultConfig {
//         applicationId 'com.sasthra.app'
//         minSdkVersion flutter.minSdkVersion
//         targetSdkVersion flutter.targetSdkVersion
//         versionCode flutter.versionCode
//         versionName flutter.versionName
//     }

//     signingConfigs {
//         release {
//             def keystoreProperties = new Properties()
//             def keystorePropertiesFile = rootProject.file('key.properties')
//             if (keystorePropertiesFile.exists()) {
//                 keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
//                 storeFile file(keystoreProperties['storeFile'])
//                 storePassword keystoreProperties['storePassword']
//                 keyAlias keystoreProperties['keyAlias']
//                 keyPassword keystoreProperties['keyPassword']
//             }
//         }
//     }

//     buildTypes {
//         release {
//             signingConfig signingConfigs.release
//             minifyEnabled false
//             shrinkResources false
//             proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
//         }
//     }
    
//     packagingOptions {
//         doNotStrip '**/*.so'
//         exclude 'lib/**/libflutter.so'
//         exclude 'lib/**/libapp.so'
//     }
    
//     // Dependencies moved to the main dependencies block below
// }

// flutter {
//     source '../..'
// }

// dependencies {
//     // Using the latest stable version of Play Core library
//     implementation 'com.google.android.play:core-ktx:1.8.1'
// }



plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'dev.flutter.flutter-gradle-plugin'
}

android {
    namespace 'com.sasthra.app'
    compileSdkVersion flutter.compileSdkVersion
    ndkVersion "27.0.12077973"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        applicationId 'com.sasthra.app'
        minSdkVersion 21
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutter.versionCode
        versionName flutter.versionName
    }

    signingConfigs {
        release {
            def keystoreProperties = new Properties()
            def keystorePropertiesFile = rootProject.file('key.properties')
            if (keystorePropertiesFile.exists()) {
                keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
                storeFile file(keystoreProperties['storeFile'])
                storePassword keystoreProperties['storePassword']
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
            }
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    packagingOptions {
        // Strip debug symbols from all native libs except libapp.so
        doNotStrip '*/armeabi-v7a/libapp.so'
        doNotStrip '*/arm64-v8a/libapp.so'
        doNotStrip '*/x86/libapp.so'
        doNotStrip '*/x86_64/libapp.so'
    }
}

flutter {
    source '../..'
}

dependencies {
    // Using the latest stable version of Play Core library
    implementation 'com.google.android.play:core-ktx:1.8.1'
}
