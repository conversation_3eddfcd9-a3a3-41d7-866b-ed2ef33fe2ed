import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

// Model for Material (unchanged)
class MaterialItem {
  final String name;
  final String? description;
  final String? fileType;
  final String? fileSize;
  final String? url;

  MaterialItem({
    required this.name,
    this.description,
    this.fileType,
    this.fileSize,
    this.url,
  });

  factory MaterialItem.fromJson(Map<String, dynamic> json) {
    return MaterialItem(
      name: json['name'] ?? 'Untitled Resource',
      description: json['description'],
      fileType: json['file_type'],
      fileSize: json['file_size'],
      url: json['url'],
    );
  }
}

// API Service (unchanged)
class EbookCentreApi {
  static const String baseUrl = 'https://testing.sasthra.in';

  Future<Map<String, List<MaterialItem>>> fetchEbookCentre({
    required String course,
    required String role,
    required String studentId,
  }) async {
    print('DEBUG: Initiating API call with course=$course, role=$role, studentId=$studentId');
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/content?course=$course&role=$role&studentId=$studentId'),
      );
      print('DEBUG: API response status: ${response.statusCode}');
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('DEBUG: API response data: $data');
        final content = data['content'] as Map<String, dynamic>;
        return content.map((key, value) => MapEntry(
              key,
              (value as List).map((item) => MaterialItem.fromJson(item)).toList(),
            ));
      } else {
        print('DEBUG: API error: ${response.statusCode} - ${response.body}');
        throw Exception('Failed to load e-book centre');
      }
    } catch (e) {
      print('DEBUG: API fetch failed: $e');
      rethrow;
    }
  }
}

// Material Card Widget
class MaterialCard extends StatefulWidget {
  final MaterialItem material;
  final VoidCallback onView;

  const MaterialCard({super.key, required this.material, required this.onView});

  @override
  _MaterialCardState createState() => _MaterialCardState();
}

class _MaterialCardState extends State<MaterialCard> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() => _isHovered = true);
        print('DEBUG: Hover started on material: ${widget.material.name}');
      },
      onExit: (_) {
        setState(() => _isHovered = false);
        print('DEBUG: Hover ended on material: ${widget.material.name}');
      },
      child: Container(
        margin: const EdgeInsets.only(top: 16), // Reduced margin
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xFFE5E7EB)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: _isHovered ? 6 : 3, // Reduced blur radius
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16), // Reduced padding
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(10), // Reduced padding
                    decoration: BoxDecoration(
                      color: const Color(0xFFF59E0B),
                      borderRadius: BorderRadius.circular(6), // Smaller radius
                      boxShadow: const [
                        BoxShadow(color: Colors.black12, blurRadius: 3),
                      ],
                    ),
                    child: const Icon(LucideIcons.bookOpen, size: 16, color: Colors.white), // Reduced icon size
                  ),
                  const SizedBox(width: 12), // Reduced spacing
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.material.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16, // Reduced font size
                            color: Color(0xFF111827),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (widget.material.description != null) ...[
                          const SizedBox(height: 3),
                          Text(
                            widget.material.description!,
                            style: const TextStyle(
                              color: Color(0xFF4B5563),
                              fontSize: 12, // Reduced font size
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 6, // Reduced spacing
                          children: [
                            Chip(
                              label: Text(
                                widget.material.fileType?.toUpperCase() ?? 'PDF',
                                style: const TextStyle(
                                  fontSize: 10, // Reduced font size
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              backgroundColor: const Color(0xFFF59E0B),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(999),
                              ),
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2), // Reduced padding
                            ),
                            if (widget.material.fileSize != null)
                              Chip(
                                label: Text(
                                  widget.material.fileSize!,
                                  style: const TextStyle(
                                    fontSize: 10, // Reduced font size
                                    color: Color(0xFF1F2937),
                                  ),
                                ),
                                backgroundColor: const Color(0xFFF3F4F6),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(999),
                                ),
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2), // Reduced padding
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(right: 16, bottom: 16, top: 6), // Reduced padding
              child: Align(
                alignment: Alignment.centerRight,
                child: ElevatedButton.icon(
                  onPressed: () {
                    print('DEBUG: View button clicked for material: ${widget.material.name}');
                    widget.onView();
                  },
                  icon: const Icon(LucideIcons.eye, size: 14), // Reduced icon size
                  label: const Text('View', style: TextStyle(fontSize: 12)), // Reduced font size
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFF59E0B),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6), // Smaller radius
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6), // Reduced padding
                  ),
                ).animate(target: _isHovered ? 1 : 0)
                    .scaleXY(begin: 1, end: 1.05, duration: 150.ms, curve: Curves.easeInOut) // Faster animation
                    .fade(begin: 0.8, end: _isHovered ? 1 : 0.8, duration: 200.ms, curve: Curves.easeInOut),
              ),
            ),
            AnimatedContainer(
              duration: const Duration(milliseconds: 200), // Faster animation
              height: 3, // Reduced height
              width: _isHovered ? double.infinity : 0,
              color: const Color(0xFFF59E0B),
            ),
          ],
        ),
      ).animate(target: _isHovered ? 1 : 0).moveY(
            begin: 0,
            end: -3, // Reduced movement
            duration: 200.ms, // Faster animation
            curve: Curves.easeInOut,
          ),
    );
  }
}

// EbookCentrePage Widget
class EbookCentrePage extends StatefulWidget {
  const EbookCentrePage({super.key});

  @override
  _EbookCentrePageState createState() => _EbookCentrePageState();
}

class _EbookCentrePageState extends State<EbookCentrePage> {
  final String course = 'NEET';
  final String role = 'student';
  final String studentId = '7bc46c34-e8e9-4d1f-a796-b6e337fb6546';
  final EbookCentreApi api = EbookCentreApi();
  String viewMode = 'grid';
  String searchTerm = '';
  List<String> activeFilters = [];
  Map<String, bool> expandedSubjects = {};
  MaterialItem? selectedMaterial;

  @override
  void initState() {
    super.initState();
    print('DEBUG: EbookCentrePage initialized with course=$course, role=$role, studentId=$studentId');
  }

  List<MaterialItem> filteredMaterials(List<MaterialItem> materials) {
    print('DEBUG: Filtering materials with searchTerm="$searchTerm", activeFilters=$activeFilters');
    var result = materials;
    if (searchTerm.isNotEmpty) {
      result = result
          .where((material) =>
              material.name.toLowerCase().contains(searchTerm.toLowerCase()) ||
              (material.description?.toLowerCase().contains(searchTerm.toLowerCase()) ?? false))
          .toList();
    }
    if (activeFilters.isNotEmpty) {
      result = result
          .where((material) => activeFilters.contains(material.fileType?.toLowerCase()))
          .toList();
    }
    print('DEBUG: Filtered materials count: ${result.length}');
    return result;
  }

  void toggleFilter(String filter) {
    setState(() {
      if (activeFilters.contains(filter)) {
        activeFilters.remove(filter);
        print('DEBUG: Removed filter: $filter');
      } else {
        activeFilters.add(filter);
        print('DEBUG: Added filter: $filter');
      }
    });
  }

  void toggleSubject(String subject) {
    setState(() {
      expandedSubjects[subject] = !(expandedSubjects[subject] ?? false);
      print('DEBUG: Toggled subject: $subject to ${expandedSubjects[subject]}');
      if (expandedSubjects[subject] == true) {
        searchTerm = '';
        print('DEBUG: Cleared search term on subject expansion');
      }
    });
  }

  void showMaterialViewer(MaterialItem material) {
    setState(() {
      selectedMaterial = material;
      print('DEBUG: Showing material viewer for: ${material.name}');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFFFDF7E3), Color(0xFFFEEBC8)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              FutureBuilder<Map<String, List<MaterialItem>>>(
                future: api.fetchEbookCentre(course: course, role: role, studentId: studentId),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    print('DEBUG: Loading data...');
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SpinKitFadingCircle(color: const Color(0xFFF59E0B), size: 40) // Reduced size
                              .animate()
                              .rotate(duration: 800.ms, curve: Curves.linear), // Faster animation
                          const SizedBox(height: 16),
                          const Text(
                            'Loading Your Library',
                            style: TextStyle(
                              fontSize: 20, // Reduced font size
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F2937),
                            ),
                          ).animate().fadeIn(duration: 200.ms).moveY(begin: 15, end: 0, duration: 200.ms),
                          const SizedBox(height: 6),
                          const Text(
                            'Curating your educational resources...',
                            style: TextStyle(color: Color(0xFF4B5563), fontSize: 12), // Reduced font size
                          ).animate().fadeIn(duration: 200.ms, delay: 300.ms),
                        ],
                      ),
                    );
                  }

                  if (snapshot.hasError) {
                    print('DEBUG: Error loading data: ${snapshot.error}');
                    return Center(
                      child: Container(
                        padding: const EdgeInsets.all(24), // Reduced padding
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10), // Smaller radius
                          border: Border(
                            left: BorderSide(color: const Color(0xFFF59E0B), width: 3),
                          ),
                          boxShadow: const [
                            BoxShadow(color: Colors.black12, blurRadius: 6),
                          ],
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CircleAvatar(
                              radius: 28, // Reduced size
                              backgroundColor: const Color(0xFFFFF7ED),
                              child: Icon(
                                LucideIcons.frown,
                                size: 28, // Reduced icon size
                                color: const Color(0xFFF59E0B),
                              ),
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              'Connection Error',
                              style: TextStyle(
                                fontSize: 20, // Reduced font size
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF111827),
                              ),
                            ),
                            const SizedBox(height: 6),
                            const Text(
                              'We couldn\'t connect to the knowledge repository. Please check your connection and try again.',
                              textAlign: TextAlign.center,
                              style: TextStyle(color: Color(0xFF4B5563), fontSize: 12), // Reduced font size
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () {
                                print('DEBUG: Retry connection clicked');
                                setState(() {});
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFFF59E0B),
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6), // Smaller radius
                                ),
                                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10), // Reduced padding
                              ),
                              child: const Text('Retry Connection', style: TextStyle(fontSize: 12)), // Reduced font size
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  final data = snapshot.data ?? {};
                  print('DEBUG: Data loaded, subjects: ${data.keys.toList()}');

                  return CustomScrollView(
                    slivers: [
                      SliverAppBar(
                        expandedHeight: 320, // Reduced height for mobile
                        flexibleSpace: FlexibleSpaceBar(
                          background: Stack(
                            children: [
                              Container(
                                decoration: const BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [Color(0xFF111827), Color(0xFF1F2937)],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                ),
                              ),
                              Positioned(
                                bottom: 0,
                                left: 0,
                                right: 0,
                                height: 120, // Reduced height
                                child: Center(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: List.generate(5, (i) {
                                      return Container(
                                        width: 80, // Reduced width
                                        height: 100 - i * 8, // Reduced height
                                        margin: EdgeInsets.only(left: (i - 2) * 30), // Reduced margin
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              const Color(0xFF4B5563),
                                              const Color(0xFF6B7280),
                                            ],
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter,
                                          ),
                                          borderRadius: BorderRadius.circular(3),
                                          border: const Border(
                                            right: BorderSide(color: Color(0xFF6B7280), width: 1.5),
                                          ),
                                          boxShadow: const [
                                            BoxShadow(
                                              color: Colors.black26,
                                              blurRadius: 3,
                                              offset: Offset(1, 1),
                                            ),
                                          ],
                                        ),
                                        child: Column(
                                          children: [
                                            Container(
                                              height: 1.5, // Reduced height
                                              margin: const EdgeInsets.only(top: 6),
                                              color: Colors.black.withOpacity(0.3),
                                            ),
                                            Container(
                                              height: 1.5, // Reduced height
                                              margin: const EdgeInsets.only(top: 3),
                                              color: Colors.black.withOpacity(0.3),
                                            ),
                                          ],
                                        ),
                                      ).animate().moveY(
                                            begin: 80, // Reduced movement
                                            end: 0,
                                            duration: 200.ms, // Faster animation
                                            delay: (i * 80).ms,
                                          ).fadeIn(duration: 200.ms);
                                    }),
                                  ),
                                ),
                              ),
                              ...List.generate(10, (i) { // Reduced number of floating elements
                                return Positioned(
                                  top: (Random().nextDouble() * 50 + 10) * MediaQuery.of(context).size.height / 100,
                                  left: Random().nextDouble() * MediaQuery.of(context).size.width,
                                  child: Transform.rotate(
                                    angle: Random().nextDouble() * 60 * pi / 180 - 30 * pi / 180,
                                    child: Container(
                                      width: Random().nextDouble() * 50 + 30, // Reduced size
                                      height: Random().nextDouble() * 25 + 15, // Reduced size
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.05),
                                        borderRadius: BorderRadius.circular(3),
                                      ),
                                    ).animate()
                                        .moveY(
                                          begin: 0,
                                          end: Random().nextDouble() * 30 - 15,
                                          duration: (Random().nextDouble() * 12 + 8).seconds, // Faster animation
                                          curve: Curves.easeInOut,
                                        )
                                        .moveX(
                                          begin: 0,
                                          end: Random().nextDouble() * 30 - 15,
                                          duration: (Random().nextDouble() * 12 + 8).seconds,
                                          curve: Curves.easeInOut,
                                        )
                                        .fade(
                                          begin: 0.3,
                                          end: 0.8,
                                          duration: (Random().nextDouble() * 12 + 8).seconds,
                                          curve: Curves.easeInOut,
                                        ),
                                  ),
                                );
                              }),
                              Positioned(
                                top: 0,
                                right: 0,
                                child: Container(
                                  width: 100, // Reduced size
                                  height: 100,
                                  child: Transform.rotate(
                                    angle: 45 * pi / 180,
                                    child: Container(
                                      decoration: const BoxDecoration(
                                        color: Color(0xFFF59E0B),
                                      ),
                                    ).animate()
                                        .scaleXY(
                                          begin: 1,
                                          end: 1.1,
                                          duration: 6.seconds, // Faster animation
                                          curve: Curves.easeInOut,
                                        ),
                                  ),
                                ),
                              ),
                              Positioned(
                                bottom: 0,
                                left: 0,
                                child: Container(
                                  width: 100, // Reduced size
                                  height: 100,
                                  child: Transform.rotate(
                                    angle: 45 * pi / 180,
                                    child: Container(
                                      decoration: const BoxDecoration(
                                        color: Color(0xFFF59E0B),
                                      ),
                                    ).animate()
                                        .scaleXY(
                                          begin: 1,
                                          end: 1.1,
                                          duration: 6.seconds,
                                          delay: 1.5.seconds,
                                          curve: Curves.easeInOut,
                                        ),
                                  ),
                                ),
                              ),
                              Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: 100, // Reduced size
                                      height: 100,
                                      child: Stack(
                                        children: [
                                          Container(
                                            decoration: BoxDecoration(
                                              color: const Color(0xFFF59E0B),
                                              borderRadius: BorderRadius.circular(10), // Smaller radius
                                              boxShadow: const [
                                                BoxShadow(color: Colors.black26, blurRadius: 6),
                                              ],
                                            ),
                                            child: const Center(
                                              child: Icon(
                                                LucideIcons.bookOpen,
                                                size: 24, // Reduced icon size
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                          Transform(
                                            alignment: Alignment.centerLeft,
                                            transform: Matrix4.identity(),
                                            child: Container(
                                              decoration: BoxDecoration(
                                                color: const Color(0xFFF59E0B),
                                                borderRadius: BorderRadius.circular(10),
                                                boxShadow: const [
                                                  BoxShadow(color: Colors.black26, blurRadius: 6),
                                                ],
                                              ),
                                              child: Align(
                                                alignment: Alignment.centerRight,
                                                child: Padding(
                                                  padding: const EdgeInsets.only(right: 12), // Reduced padding
                                                  child: Container(
                                                    width: 3, // Reduced width
                                                    height: 28, // Reduced height
                                                    decoration: BoxDecoration(
                                                      color: const Color(0xFF78350F).withOpacity(0.5),
                                                      borderRadius: BorderRadius.circular(999),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 24), // Reduced spacing
                                    Stack(
                                      clipBehavior: Clip.none,
                                      children: [
                                        const Text(
                                          'E-Books Library',
                                          style: TextStyle(
                                            fontSize: 24, // Reduced font size
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ).animate().fadeIn(duration: 200.ms, delay: 600.ms).moveY(
                                              begin: 15,
                                              end: 0,
                                              duration: 200.ms,
                                              delay: 600.ms,
                                              curve: Curves.easeInOut,
                                            ),
                                        Positioned(
                                          left: 0,
                                          right: 0,
                                          bottom: -10, // Adjusted position
                                          child: Container(
                                            height: 6, // Reduced height
                                            decoration: const BoxDecoration(
                                              color: Color(0xFFF59E0B),
                                              borderRadius: BorderRadius.all(Radius.circular(999)),
                                            ),
                                          ).animate().scaleX(
                                                begin: 0,
                                                end: 1,
                                                duration: 600.ms, // Faster animation
                                                delay: 800.ms,
                                              ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 24),
                                    Text(
                                      role == 'student'
                                          ? 'Explore our collection of digital knowledge'
                                          : 'Manage and distribute learning materials',
                                      style: const TextStyle(
                                        fontSize: 14, // Reduced font size
                                        color: Color(0xFFD1D5DB),
                                      ),
                                      textAlign: TextAlign.center,
                                    ).animate().fadeIn(duration: 200.ms, delay: 1000.ms),
                                    const SizedBox(height: 24),
                                    Container(
                                      width: MediaQuery.of(context).size.width * 0.9, // Responsive width
                                      constraints: const BoxConstraints(maxWidth: 350), // Reduced max width
                                      padding: const EdgeInsets.symmetric(horizontal: 12), // Reduced padding
                                      child: TextField(
                                        onChanged: (value) {
                                          setState(() {
                                            searchTerm = value;
                                            print('DEBUG: Search term updated: $value');
                                          });
                                        },
                                        decoration: InputDecoration(
                                          hintText: 'Search e-books, guides...',
                                          hintStyle: const TextStyle(color: Color(0xFF9CA3AF), fontSize: 12), // Reduced font size
                                          prefixIcon: const Icon(
                                            LucideIcons.search,
                                            color: Color(0xFF9CA3AF),
                                            size: 16, // Reduced icon size
                                          ),
                                          suffixIcon: Container(
                                            margin: const EdgeInsets.only(right: 6),
                                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3), // Reduced padding
                                            decoration: BoxDecoration(
                                              color: const Color(0xFFF59E0B).withOpacity(0.1),
                                              borderRadius: BorderRadius.circular(6),
                                              border: Border.all(
                                                color: const Color(0xFFFBBF24).withOpacity(0.3),
                                              ),
                                            ),
                                            child: const Text(
                                              '⌘K',
                                              style: TextStyle(
                                                color: Color(0xFFFBBF24),
                                                fontSize: 10, // Reduced font size
                                              ),
                                            ).animate()
                                                .fade(
                                                  begin: 0.6,
                                                  end: 1,
                                                  duration: 1500.ms, // Faster animation
                                                  curve: Curves.easeInOut,
                                                ),
                                          ),
                                          filled: true,
                                          fillColor: const Color(0xFF1F2937).withOpacity(0.5),
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(10), // Smaller radius
                                            borderSide: const BorderSide(
                                              color: Color(0xFF374151),
                                            ),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(10),
                                            borderSide: const BorderSide(
                                              color: Color(0xFF374151),
                                            ),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(10),
                                            borderSide: const BorderSide(
                                              color: Color(0xFFFBBF24),
                                              width: 1.5, // Thinner border
                                            ),
                                          ),
                                        ),
                                        style: const TextStyle(color: Colors.white, fontSize: 12), // Reduced font size
                                      ),
                                    ).animate().fadeIn(duration: 200.ms, delay: 1200.ms).moveY(
                                          begin: 8,
                                          end: 0,
                                          duration: 200.ms,
                                          delay: 1200.ms,
                                          curve: Curves.easeInOut,
                                        ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        pinned: true,
                      ),
                      SliverToBoxAdapter(
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 16), // Reduced margins
                          padding: const EdgeInsets.all(10), // Reduced padding
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(6), // Smaller radius
                            boxShadow: const [
                              BoxShadow(color: Colors.black12, blurRadius: 3),
                            ],
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  PopupMenuButton<String>(
                                    onSelected: (value) {
                                      toggleFilter(value);
                                      print('DEBUG: Filter selected: $value');
                                    },
                                    itemBuilder: (context) => ['pdf', 'doc', 'ppt'].map((type) {
                                      return PopupMenuItem(
                                        value: type,
                                        child: Row(
                                          children: [
                                            if (activeFilters.contains(type))
                                              Container(
                                                width: 6, // Reduced size
                                                height: 6,
                                                margin: const EdgeInsets.only(right: 6),
                                                decoration: BoxDecoration(
                                                  color: const Color(0xFFF59E0B),
                                                  shape: BoxShape.circle,
                                                ),
                                              ),
                                            Text(
                                              type.toUpperCase(),
                                              style: const TextStyle(fontSize: 12), // Reduced font size
                                            ),
                                          ],
                                        ),
                                      );
                                    }).toList(),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6), // Reduced padding
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFFFF7ED),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: const Row(
                                        children: [
                                          Icon(LucideIcons.filter, size: 16, color: Color(0xFFF59E0B)), // Reduced icon size
                                          SizedBox(width: 6),
                                          Text(
                                            'Filters',
                                            style: TextStyle(
                                              color: Color(0xFFF59E0B),
                                              fontSize: 12, // Reduced font size
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 6),
                                  if (activeFilters.isNotEmpty) ...[
                                    Wrap(
                                      spacing: 6, // Reduced spacing
                                      children: activeFilters.map((filter) {
                                        return Chip(
                                          label: Text(
                                            filter.toUpperCase(),
                                            style: const TextStyle(
                                              color: Color(0xFF92400E),
                                              fontSize: 10, // Reduced font size
                                            ),
                                          ),
                                          deleteIcon: const Icon(
                                            LucideIcons.x,
                                            size: 12, // Reduced icon size
                                            color: Color(0xFFF59E0B),
                                          ),
                                          onDeleted: () {
                                            toggleFilter(filter);
                                            print('DEBUG: Removed filter chip: $filter');
                                          },
                                          backgroundColor: const Color(0xFFFFF7ED),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(999),
                                          ),
                                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2), // Reduced padding
                                        );
                                      }).toList(),
                                    ),
                                    const SizedBox(width: 6),
                                    TextButton(
                                      onPressed: () {
                                        setState(() {
                                          activeFilters.clear();
                                          print('DEBUG: Cleared all filters');
                                        });
                                      },
                                      child: const Text(
                                        'Clear all',
                                        style: TextStyle(
                                          color: Color(0xFFF59E0B),
                                          fontSize: 10, // Reduced font size
                                        ),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                              Container(
                                padding: const EdgeInsets.all(3), // Reduced padding
                                decoration: BoxDecoration(
                                  color: const Color(0xFFFFF7ED),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Row(
                                  children: [
                                    IconButton(
                                      onPressed: () {
                                        setState(() {
                                          viewMode = 'grid';
                                          print('DEBUG: Switched to grid view');
                                        });
                                      },
                                      icon: Icon(
                                        LucideIcons.grid,
                                        size: 16, // Reduced icon size
                                        color: viewMode == 'grid'
                                            ? const Color(0xFFF59E0B)
                                            : const Color(0xFF4B5563),
                                      ),
                                      style: ButtonStyle(
                                        backgroundColor: WidgetStateProperty.resolveWith((states) {
                                          if (viewMode == 'grid') {
                                            return const Color(0xFFF59E0B);
                                          }
                                          if (states.contains(WidgetState.hovered)) {
                                            return const Color(0xFFFFF7ED);
                                          }
                                          return null;
                                        }),
                                        foregroundColor: WidgetStateProperty.resolveWith((states) {
                                          if (viewMode == 'grid') return Colors.white;
                                          return const Color(0xFFF59E0B);
                                        }),
                                        shape: WidgetStateProperty.all(
                                          RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(6),
                                          ),
                                        ),
                                      ),
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        setState(() {
                                          viewMode = 'list';
                                          print('DEBUG: Switched to list view');
                                        });
                                      },
                                      icon: Icon(
                                        LucideIcons.list,
                                        size: 16, // Reduced icon size
                                        color: viewMode == 'list'
                                            ? const Color(0xFFF59E0B)
                                            : const Color(0xFF4B5563),
                                      ),
                                      style: ButtonStyle(
                                        backgroundColor: WidgetStateProperty.resolveWith((states) {
                                          if (viewMode == 'list') {
                                            return const Color(0xFFF59E0B);
                                          }
                                          if (states.contains(WidgetState.hovered)) {
                                            return const Color(0xFFFFF7ED);
                                          }
                                          return null;
                                        }),
                                        foregroundColor: WidgetStateProperty.resolveWith((states) {
                                          if (viewMode == 'list') return Colors.white;
                                          return const Color(0xFFF59E0B);
                                        }),
                                        shape: WidgetStateProperty.all(
                                          RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(6),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final subject = data.keys.elementAt(index);
                            final files = data[subject] ?? [];
                            return Container(
                              margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6), // Reduced margins
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(color: const Color(0xFFFFF7ED)),
                                boxShadow: const [
                                  BoxShadow(color: Colors.black12, blurRadius: 3),
                                ],
                              ),
                              child: ExpansionTile(
                                onExpansionChanged: (expanded) {
                                  toggleSubject(subject);
                                },
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                collapsedBackgroundColor: Colors.transparent,
                                backgroundColor: Colors.transparent,
                                leading: Icon(
                                  LucideIcons.chevronDown,
                                  color: const Color(0xFFF59E0B),
                                  size: 16, // Reduced icon size
                                ).animate().rotate(
                                      begin: expandedSubjects[subject] == true ? 0.5 : 0,
                                      end: expandedSubjects[subject] == true ? 0.5 : 0,
                                      duration: 200.ms, // Faster animation
                                      curve: Curves.easeInOut,
                                    ),
                                title: Row(
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            subject,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 16, // Reduced font size
                                              color: Color(0xFF111827),
                                            ),
                                          ),
                                          Text(
                                            '${files.length} resource${files.length != 1 ? 's' : ''}',
                                            style: const TextStyle(
                                              color: Color(0xFFF59E0B),
                                              fontSize: 12, // Reduced font size
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3), // Reduced padding
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFFFF7ED),
                                        borderRadius: BorderRadius.circular(999),
                                      ),
                                      child: Row(
                                        children: [
                                          Text(
                                            expandedSubjects[subject] == true ? 'Collapse' : 'Expand',
                                            style: const TextStyle(
                                              color: Color(0xFF92400E),
                                              fontSize: 10, // Reduced font size
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          const SizedBox(width: 3),
                                          Icon(
                                            LucideIcons.arrowRight,
                                            size: 12, // Reduced icon size
                                            color: const Color(0xFF92400E),
                                          ).animate().rotate(
                                                begin: expandedSubjects[subject] == true ? 0.25 : 0,
                                                end: expandedSubjects[subject] == true ? 0.25 : 0,
                                                duration: 200.ms,
                                                curve: Curves.easeInOut,
                                              ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.all(12), // Reduced padding
                                    child: filteredMaterials(files).isNotEmpty
                                        ? viewMode == 'grid'
                                            ? GridView.builder(
                                                shrinkWrap: true,
                                                physics: const NeverScrollableScrollPhysics(),
                                                gridDelegate:
                                                    const SliverGridDelegateWithMaxCrossAxisExtent(
                                                  maxCrossAxisExtent: 350, // Reduced max extent
                                                  crossAxisSpacing: 12,
                                                  mainAxisSpacing: 12,
                                                  childAspectRatio: 0.8, // Adjusted for mobile
                                                ),
                                                itemCount: filteredMaterials(files).length,
                                                itemBuilder: (context, idx) {
                                                  return MaterialCard(
                                                    material: filteredMaterials(files)[idx],
                                                    onView: () =>
                                                        showMaterialViewer(filteredMaterials(files)[idx]),
                                                  );
                                                },
                                              )
                                            : ListView.builder(
                                                shrinkWrap: true,
                                                physics: const NeverScrollableScrollPhysics(),
                                                itemCount: filteredMaterials(files).length,
                                                itemBuilder: (context, idx) {
                                                  return MaterialCard(
                                                    material: filteredMaterials(files)[idx],
                                                    onView: () =>
                                                        showMaterialViewer(filteredMaterials(files)[idx]),
                                                  );
                                                },
                                              )
                                        : Column(
                                            children: [
                                              CircleAvatar(
                                                radius: 28, // Reduced size
                                                backgroundColor: const Color(0xFFFFF7ED),
                                                child: Icon(
                                                  LucideIcons.frown,
                                                  size: 20, // Reduced icon size
                                                  color: const Color(0xFFF59E0B),
                                                ),
                                              ),
                                              const SizedBox(height: 12),
                                              const Text(
                                                'No matching materials',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 14, // Reduced font size
                                                  color: Color(0xFF374151),
                                                ),
                                              ),
                                              const SizedBox(height: 3),
                                              const Text(
                                                'Try adjusting your filters or search term',
                                                style: TextStyle(
                                                  color: Color(0xFFF59E0B),
                                                  fontSize: 12, // Reduced font size
                                                ),
                                              ),
                                            ],
                                          ).animate().fadeIn(duration: 200.ms),
                                  ),
                                ],
                              ),
                            );
                          },
                          childCount: data.length,
                        ),
                      ),
                    ],
                  );
                },
              ),
              if (selectedMaterial != null)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedMaterial = null;
                      print('DEBUG: Closed material viewer by tapping background');
                    });
                  },
                  child: Container(
                    color: Colors.black.withOpacity(0.8),
                    child: Center(
                      child: GestureDetector(
                        onTap: () {},
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.95, // More compact for mobile
                          constraints: const BoxConstraints(maxWidth: 1000), // Reduced max width
                          height: MediaQuery.of(context).size.height * 0.85, // Reduced height
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10), // Smaller radius
                            border: Border(
                              top: BorderSide(color: const Color(0xFFF59E0B), width: 3),
                            ),
                            boxShadow: const [
                              BoxShadow(color: Colors.black26, blurRadius: 10),
                            ],
                          ),
                          child: Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(12), // Reduced padding
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      selectedMaterial!.name,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16, // Reduced font size
                                        color: Color(0xFF111827),
                                      ),
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        setState(() {
                                          selectedMaterial = null;
                                          print('DEBUG: Closed material viewer via button');
                                        });
                                      },
                                      icon: const Icon(
                                        LucideIcons.x,
                                        size: 18, // Reduced icon size
                                        color: Color(0xFF6B7280),
                                      ),
                                      style: ButtonStyle(
                                        backgroundColor: WidgetStateProperty.resolveWith((states) {
                                          if (states.contains(WidgetState.hovered)) {
                                            return const Color(0xFFFFF7ED);
                                          }
                                          return null;
                                        }),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Divider(height: 1, color: Color(0xFFFFF7ED)),
                              Expanded(
                                child: selectedMaterial!.url != null
                                    ? WebViewWidget(
                                        controller: WebViewController()
                                          ..setJavaScriptMode(JavaScriptMode.unrestricted)
                                          ..loadRequest(Uri.parse(selectedMaterial!.url!)),
                                      )
                                    : Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          CircleAvatar(
                                            radius: 32, // Reduced size
                                            backgroundColor: const Color(0xFFFFF7ED),
                                            child: Icon(
                                              LucideIcons.fileText,
                                              size: 28, // Reduced icon size
                                              color: const Color(0xFFF59E0B),
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          const Text(
                                            'Preview Unavailable',
                                            style: TextStyle(
                                              fontSize: 18, // Reduced font size
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF111827),
                                            ),
                                          ),
                                          const SizedBox(height: 6),
                                          const Text(
                                            'This resource cannot be displayed in the viewer.',
                                            style: TextStyle(
                                              color: Color(0xFFF59E0B),
                                              fontSize: 12, // Reduced font size
                                            ),
                                          ),
                                          if (selectedMaterial!.url != null) ...[
                                            const SizedBox(height: 16),
                                            ElevatedButton.icon(
                                              onPressed: () async {
                                                print('DEBUG: Download button clicked for: ${selectedMaterial!.url}');
                                                final url = Uri.parse(selectedMaterial!.url!);
                                                if (await canLaunchUrl(url)) {
                                                  await launchUrl(url, mode: LaunchMode.externalApplication);
                                                } else {
                                                  print('DEBUG: Failed to launch URL: ${selectedMaterial!.url}');
                                                  ScaffoldMessenger.of(context).showSnackBar(
                                                    const SnackBar(
                                                      content: Text('Could not launch the resource URL.'),
                                                    ),
                                                  );
                                                }
                                              },
                                              icon: const Icon(LucideIcons.download, size: 16), // Reduced icon size
                                              label: const Text('Download Resource', style: TextStyle(fontSize: 12)), // Reduced font size
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: const Color(0xFFF59E0B),
                                                foregroundColor: Colors.white,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius: BorderRadius.circular(6),
                                                ),
                                                padding: const EdgeInsets.symmetric(
                                                  horizontal: 16,
                                                  vertical: 10,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ],
                                      ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ).animate().fadeIn(duration: 200.ms),
                ),
            ],
          ),
        ),
      ),
    );
  }
}