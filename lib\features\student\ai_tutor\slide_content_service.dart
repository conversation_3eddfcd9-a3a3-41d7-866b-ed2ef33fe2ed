// import 'dart:convert';
// import 'package:http/http.dart' as http;

// class SlideRepository {
//   static const String baseUrl = "https://testing.sasthra.in";

//   /// Get endpoint based on subject and role
//   String _getEndpoint(String subject, String role) {
//     final endpoints = {
//       "Mathematics":
//           "$baseUrl/studentpanel/mathapi/explain_slides",
//       "Chemistry": 
//            "$baseUrl/studentpanel/chemapi/explain_slides",
//       "Physics":
//           "$baseUrl/studentpanel/physicsapi/explain_slides",
//       "Biology": 
//            "$baseUrl/studentpanel/bioapi/explain_slides"
//     };

//     return endpoints[subject] ?? "";
//   }

//   /// Fetch slide translations
//   Future<Map<String, String>> fetchTranslations({
//     required String subject,
//     required String role,
//     required String processorSelectorId,
//     required String language,
//   }) async {
//     final endpoint = _getEndpoint(subject, role);

//     if (endpoint.isEmpty) {
//       print("Invalid subject: $subject");
//       return {};
//     }

//     try {
//       final response = await http.post(
//         Uri.parse(endpoint),
//         headers: {"Content-Type": "application/json"},
//         body: jsonEncode({
//           "processor_selector_id": processorSelectorId,
//           "language": language,
//         }),
//       );

//       if (response.statusCode == 200) {
//         final data = jsonDecode(response.body) as Map<String, dynamic>;
//         return data.map((key, value) => MapEntry(key, value.toString()));
//       } else {
//         print("Failed to fetch slides: ${response.body}");
//       }
//     } catch (e) {
//       print("Error fetching slides: $e");
//     }
//     return {};
//   }
// }
