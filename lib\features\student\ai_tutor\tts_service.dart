import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:audioplayers/audioplayers.dart';

class TTSService {
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;
  bool _isLoading = false;

  bool get isPlaying => _isPlaying;
  bool get isLoading => _isLoading;

  Future<void> play(String text, Function onComplete) async {
    try {
      _isLoading = true;
      await _audioPlayer.stop();

      final response = await http.post(
        Uri.parse("https://sasthra.in/tts"),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({"text": text}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        String base64Audio = data["audio"];
        Uint8List audioBytes = base64Decode(base64Audio);

        await _audioPlayer.play(BytesSource(audioBytes));
        _isPlaying = true;
        _isLoading = false;

        _audioPlayer.onPlayerComplete.listen((_) {
          _isPlaying = false;
          onComplete();
        });
      } else {
        _isLoading = false;
        print("TTS failed: ${response.body}");
      }
    } catch (e) {
      _isLoading = false;
      print("Error in TTS: $e");
    }
  }

  Future<void> pause() async {
    await _audioPlayer.pause();
    _isPlaying = false;
  }

  Future<void> stop() async {
    await _audioPlayer.stop();
    _isPlaying = false;
  }

  void dispose() => _audioPlayer.dispose();
}
