/**
 * app_router.dart - Application Navigation and Routing System
 *
 * English: This file contains the complete navigation and routing system for the Sasthra
 * mobile application. It uses GoRouter for declarative routing with authentication guards,
 * role-based access control, and dynamic page routing. It handles navigation between all
 * features including authentication flows, role-specific dashboards, and feature pages.
 *
 * Tanglish: Inga naama app oda complete navigation system irukku. GoRouter use pannitu
 * authentication check pannum, role based access control pannum. Login, dashboard,
 * different role pages - ellam inga route pannirukkom. User authentication status
 * check pannitu correct page ku navigate pannum.
 *
 * Key Features:
 * - Authentication-aware routing with guards
 * - Role-based page access control
 * - Dynamic route generation for different user roles
 * - Splash screen and authentication flow handling
 * - Error page routing for invalid routes
 * - Deep linking support for web and mobile
 *
 * Supported Routes:
 * - Authentication: /login, /otp, /splash
 * - Role-specific: /{role}/{page} (e.g., /director/overview, /student/dashboard)
 * - Common: /dashboard, /error
 */

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sasthra/features/student/e-book/ebook_page.dart';
import '../services/auth_service.dart';
import '../models/auth_models.dart';
import '../utils/logger.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/otp_page.dart';
import '../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../../features/splash/presentation/pages/splash_page.dart';
import '../../features/kota_teacher/overview/overview_page.dart';
import '../../features/kota_teacher/mapping_centers/mapping_centers_page.dart';
import '../../features/kota_teacher/schedule_events/schedule_events_page.dart';
import '../../features/kota_teacher/live_streaming/live_streaming_page.dart';
import '../../features/kota_teacher/zoom_call/zoom_call_page.dart';
import '../../features/student/overview/overview_page.dart';
import '../../features/student/live_streaming/live_streaming_page.dart';
import '../../features/student/ai_tutor/ai_tutor_page.dart';
import '../../features/student/mock_test_simulation/mock_test_simulation_page.dart';
import '../../features/student/student_community/student_community_page.dart';
import '../../features/student/dashboard/dashboard_page.dart';
import '../../features/student/virtual_labs/view/virtual_lab_page.dart';
import '../../features/student/question_generator/view/question_generator_view.dart';
import '../../features/student/e-book/ebook_page.dart';


// Director imports
import '../../features/director/overview/overview_page.dart';
import '../../features/director/director_inbox/director_inbox_page.dart';
import '../../features/director/batch/batch_page.dart';
import '../../features/director/course/course_page.dart';
import '../../features/director/add_center/add_center_page.dart';
import '../../features/director/add_kota_teachers/add_kota_teachers_page.dart';
import '../../features/director/add_mentor/add_mentor_page.dart';
import '../../features/director/list_center/list_center_page.dart';
import '../../features/director/list_kota/list_kota_page.dart';
import '../../features/director/list_mentor/list_mentor_page.dart';
import '../../features/director/process_selector/process_selector_page.dart';
import '../../features/director/events/events_page.dart';
import '../../features/director/subjects/subjects_page.dart';
import '../../features/director/dashboard/dashboard_page.dart';

// Center Counselor imports
import '../../features/center_counselor/overview/overview_page.dart';
import '../../features/center_counselor/add_students/add_students_page.dart';
import '../../features/center_counselor/add_faculty/add_faculty_page.dart';
import '../../features/center_counselor/list_students/list_students_page.dart';
import '../../features/center_counselor/list_faculty/list_faculty_page.dart';
import '../../features/center_counselor/kota_teachers/kota_teachers_page.dart';

// Faculty imports
import '../../features/faculty/overview/overview_page.dart';
import '../../features/faculty/live_viewer/live_viewer_page.dart';
import '../../features/faculty/paper_based_evaluator/paper_based_evaluator_page.dart';
import '../../features/faculty/evaluator_result/evaluator_result_page.dart';
import '../../features/faculty/paper_based_test/paper_based_test_page.dart';
import '../../features/faculty/ai_tutor/ai_tutor_page.dart';
import '../../features/faculty/zoom_viewer/zoom_viewer_page.dart';

// Mendor imports
import '../../features/mendor/mentor_dashboard/mentor_dashboard_page.dart';
import '../../features/common/presentation/pages/error_page.dart';
import '../../features/common/presentation/pages/role_specific_page.dart';

// Router provider
final appRouterProvider = Provider<GoRouter>((ref) {
  final authService = AuthService();

  return GoRouter(
    initialLocation: '/splash',
    debugLogDiagnostics: true,
    redirect: (context, state) {
      final authState = authService.state;
      final isAuthenticated = authService.isAuthenticated;
      final currentLocation = state.uri.path;

      AppLogger.navigation(
          'Redirect check', 'From: $currentLocation, Auth: $authState');

      // Handle splash screen
      if (currentLocation == '/splash') {
        return null; // Allow splash screen
      }

      // Handle authentication states
      switch (authState) {
        case AuthState.initial:
        case AuthState.loading:
          return '/splash';

        case AuthState.unauthenticated:
          if (currentLocation.startsWith('/auth')) {
            return null; // Allow auth pages
          }
          return '/auth/login';

        case AuthState.otpRequired:
          if (currentLocation == '/auth/otp') {
            return null; // Allow OTP page
          }
          return '/auth/otp';

        case AuthState.authenticated:
          if (isAuthenticated) {
            if (currentLocation.startsWith('/auth') ||
                currentLocation == '/splash') {
              return '/dashboard'; // Redirect to dashboard if trying to access auth pages
            }
            return null; // Allow access to protected pages
          }
          return '/auth/login';

        case AuthState.error:
          if (currentLocation.startsWith('/auth')) {
            return null; // Allow auth pages to handle errors
          }
          return '/auth/login';
      }
    },
    routes: [
      // Splash Route
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Authentication Routes
      GoRoute(
        path: '/auth',
        redirect: (context, state) => '/auth/login',
      ),
      GoRoute(
        path: '/auth/login',
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: '/auth/otp',
        name: 'otp',
        builder: (context, state) => const OtpPage(),
      ),

      // Dashboard Route
      GoRoute(
        path: '/dashboard',
        name: 'dashboard',
        builder: (context, state) => const DashboardPage(),
        routes: [
          // Role-specific routes will be added dynamically
          GoRoute(
            path: 'faculty/:page',
            name: 'faculty',
            builder: (context, state) {
              final page = state.pathParameters['page'] ?? '';
              return _buildRolePage('faculty', page);
            },
          ),
          GoRoute(
            path: 'student/:page',
            name: 'student',
            builder: (context, state) {
              final page = state.pathParameters['page'] ?? '';
              return _buildRolePage('student', page);
            },
          ),
          GoRoute(
            path: 'director/:page',
            name: 'director',
            builder: (context, state) {
              final page = state.pathParameters['page'] ?? '';
              return _buildRolePage('director', page);
            },
          ),
          GoRoute(
            path: 'kota_teacher/:page',
            name: 'kota_teacher',
            builder: (context, state) {
              final page = state.pathParameters['page'] ?? '';
              return _buildRolePage('kota_teacher', page);
            },
          ),
          GoRoute(
            path: 'kota-teacher/:page',
            name: 'kota-teacher',
            builder: (context, state) {
              final page = state.pathParameters['page'] ?? '';
              return _buildRolePage('kota_teacher', page);
            },
          ),
          GoRoute(
            path: 'mendor/:page',
            name: 'mendor',
            builder: (context, state) {
              final page = state.pathParameters['page'] ?? '';
              return _buildRolePage('mendor', page);
            },
          ),
          GoRoute(
            path: 'center_counselor/:page',
            name: 'center_counselor',
            builder: (context, state) {
              final page = state.pathParameters['page'] ?? '';
              return _buildRolePage('center_counselor', page);
            },
          ),
          GoRoute(
            path: 'center-counselor/:page',
            name: 'center-counselor',
            builder: (context, state) {
              final page = state.pathParameters['page'] ?? '';
              return _buildRolePage('center_counselor', page);
            },
          ),
          GoRoute(
            path: 'parent/:page',
            name: 'parent',
            builder: (context, state) {
              final page = state.pathParameters['page'] ?? '';
              return _buildRolePage('parent', page);
            },
          ),
        ],
      ),

      // Catch-all route
      GoRoute(
        path: '/:path(.*)',
        builder: (context, state) => const NotFoundPage(),
      ),
    ],
    errorBuilder: (context, state) => ErrorPage(error: state.error),
  );
});

// Helper function to build role-specific pages
Widget _buildRolePage(String role, String page) {
  // Convert hyphens back to underscores for consistency with page names
  final normalizedPage = page.replaceAll('-', '_');
  AppLogger.navigation('Building role page', '$role/$normalizedPage');

  // Handle Kota Teacher pages
  if (role == 'kota_teacher') {
    switch (normalizedPage) {
      case 'overview':
        return const KotaTeacherOverviewPage();
      case 'mapping_centers':
        return const MappingCentersPage();
      case 'schedule_events':
        return const ScheduleEventsPage();
      case 'live_streaming':
        return const LiveStreamingPage();
      case 'zoom_call':
        return const ZoomCallPage();
      default:
        return const KotaTeacherOverviewPage(); // Default to overview
    }
  }
print("Normalized page is: '$normalizedPage'");

  // Handle Student pages
  if (role == 'student') {
    switch (normalizedPage) {
      case 'overview':
        return const StudentOverviewPage();
      case 'live_streaming':
        return const StudentLiveStreamingPage();
      case 'ai_tutor':
        return const StudentAITutorPage();
      case 'mock_test_simulation':
        return const MockTestSimulationPage();
      case 'student_community':
        return const StudentCommunityPage();
      case 'dashboard':
        return const StudentDashboardPage();
      case 'virtual_labs':
        return const VirtualLabsPage();
      case 'question_generator':
        return const QuestionGeneratorPage();
      case 'ebook_centre':
        return const EbookCentrePage();
      default:
        return const StudentOverviewPage(); // Default to overview
    }
  }

  // Handle Director pages
  if (role == 'director') {
    switch (normalizedPage) {
      case 'overview':
        return const DirectorOverviewPage();
      case 'director_inbox':
        return const DirectorInboxPage();
      case 'batch':
        return const BatchPage();
      case 'course':
        return const CoursePage();
      case 'add_center':
        return const AddCenterPage();
      case 'add_kota_teachers':
        return const AddKotaTeachersPage();
      case 'add_mentor':
        return const AddMentorPage();
      case 'list_center':
        return const ListCenterPage();
      case 'list_kota':
        return const ListKotaPage();
      case 'list_mentor':
        return const ListMentorPage();
      case 'process_selector':
        return const ProcessSelectorPage();
      case 'events':
        return const EventsPage();
      case 'subjects':
        return const SubjectsPage();
      case 'dashboard':
        return const DirectorDashboardPage();
      default:
        return const DirectorOverviewPage(); // Default to overview
    }
  }

  // Handle Center Counselor pages
  if (role == 'center_counselor') {
    switch (normalizedPage) {
      case 'overview':
        return const CenterCounselorOverviewPage();
      case 'add_students':
        return const AddStudentsPage();
      case 'add_faculty':
        return const AddFacultyPage();
      case 'list_students':
        return const ListStudentsPage();
      case 'list_faculty':
        return const ListFacultyPage();
      case 'kota_teachers':
        return const KotaTeachersPage();
      default:
        return const CenterCounselorOverviewPage(); // Default to overview
    }
  }

  // Handle Faculty pages
  if (role == 'faculty') {
    switch (normalizedPage) {
      case 'overview':
        return const FacultyOverviewPage();
      case 'live_viewer':
        return const LiveViewerPage();
      case 'paper_based_evaluator':
        return const PaperBasedEvaluatorPage();
      case 'evaluator_result':
        return const EvaluatorResultPage();
      case 'paper_based_test':
        return const PaperBasedTestPage();
      case 'ai_tutor':
        return const FacultyAITutorPage();
      case 'zoom_viewer':
        return const ZoomViewerPage();
      default:
        return const FacultyOverviewPage(); // Default to overview
    }
  }

  // Handle Mendor pages
  if (role == 'mendor') {
    switch (normalizedPage) {
      case 'mentor_dashboard':
        return const MentorDashboardPage();
      default:
        return const MentorDashboardPage(); // Default to mentor dashboard
    }
  }

  // For other roles, return placeholder for now
  return RoleSpecificPage(role: role, page: normalizedPage);
}

// Placeholder pages
class NotFoundPage extends StatelessWidget {
  const NotFoundPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Page Not Found')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              '404 - Page Not Found',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('The requested page could not be found.'),
          ],
        ),
      ),
    );
  }
}

class ErrorPage extends StatelessWidget {
  final Exception? error;

  const ErrorPage({Key? key, this.error}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            const Text(
              'An Error Occurred',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(error?.toString() ?? 'Unknown error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    );
  }
}

class RoleSpecificPage extends StatelessWidget {
  final String role;
  final String page;

  const RoleSpecificPage({
    Key? key,
    required this.role,
    required this.page,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${_formatRole(role)} - ${_formatPage(page)}'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getRoleIcon(role),
              size: 64,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              '${_formatRole(role)} Dashboard',
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text('Page: ${_formatPage(page)}'),
            const SizedBox(height: 16),
            const Text('This page is under development.'),
          ],
        ),
      ),
    );
  }

  String _formatRole(String role) {
    switch (role) {
      case 'kota_teacher':
        return 'Kota Teacher';
      case 'center_counselor':
        return 'Center Counselor';
      default:
        return role.substring(0, 1).toUpperCase() + role.substring(1);
    }
  }

  String _formatPage(String page) {
    return page
        .replaceAll('-', ' ')
        .split(' ')
        .map((word) => word.substring(0, 1).toUpperCase() + word.substring(1))
        .join(' ');
  }

  IconData _getRoleIcon(String role) {
    switch (role) {
      case 'faculty':
        return Icons.person;
      case 'kota_teacher':
        return Icons.school;
      case 'student':
        return Icons.person_outline;
      case 'director':
        return Icons.business;
      case 'mendor':
        return Icons.psychology;
      case 'center_counselor':
        return Icons.location_on;
      case 'parent':
        return Icons.family_restroom;
      default:
        return Icons.dashboard;
    }
  }
}
