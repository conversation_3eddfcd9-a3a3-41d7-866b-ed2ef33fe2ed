import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/models/menu_models.dart';
import '../../../../core/models/auth_models.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/config/app_config.dart';

class RoleBasedDrawer extends StatelessWidget {
  final List<MenuItem> menuItems;
  final bool isLoading;
  final String? error;
  final VoidCallback? onRetry;

  const RoleBasedDrawer({
    Key? key,
    required this.menuItems,
    required this.isLoading,
    this.error,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final authService = AuthService();
    final user = authService.currentUser;

    return Drawer(
      backgroundColor: AppTheme.surfaceColor,
      child: Safe<PERSON>rea(
        child: Column(
          children: [
            // Header Section
            _buildHeader(context, user),
            
            const Divider(height: 1),
            
            // Menu Items Section
            Expanded(
              child: _buildMenuSection(context),
            ),
            
            const Divider(height: 1),
            
            // Footer Section
            _buildFooter(context, authService),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, UserData? user) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile Avatar
          CircleAvatar(
            radius: 30,
            backgroundColor: Colors.white.withOpacity(0.2),
            child: Text(
              user?.displayName.substring(0, 1).toUpperCase() ?? 'U',
              style: AppTheme.headingMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // User Name
          Text(
            user?.displayName ?? 'User',
            style: AppTheme.headingSmall.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 4),
          
          // User Role
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              AppConfig.roleDisplayNames[user?.role] ?? user?.role?.toUpperCase() ?? 'UNKNOWN',
              style: AppTheme.bodySmall.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn().slideX(begin: -0.3);
  }

  Widget _buildMenuSection(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading menu...'),
          ],
        ),
      );
    }

    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: AppTheme.errorColor,
            ),
            const SizedBox(height: 16),
            Text(
              error!,
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.errorColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (menuItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.menu_open,
              size: 48,
              color: AppTheme.textTertiary,
            ),
            const SizedBox(height: 16),
            Text(
              'No menu items available',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textTertiary,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: menuItems.length,
      itemBuilder: (context, index) {
        final menuItem = menuItems[index];
        return _buildMenuItem(context, menuItem, index);
      },
    );
  }

  Widget _buildMenuItem(BuildContext context, MenuItem menuItem, int index) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Icon(
          _getMenuIcon(menuItem.iconName),
          color: AppTheme.primaryColor,
          size: 20,
        ),
      ),
      title: Text(
        menuItem.name,
        style: AppTheme.bodyMedium.copyWith(
          color: AppTheme.textPrimary,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: menuItem.hasSubmenu
          ? Text(
              '${menuItem.submenu!.length} items',
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondary,
              ),
            )
          : null,
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppTheme.textTertiary,
      ),
      onTap: () {
        _handleMenuItemTap(context, menuItem);
      },
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .slideX(begin: -0.2);
  }

  void _handleMenuItemTap(BuildContext context, MenuItem menuItem) {
    Navigator.of(context).pop(); // Close drawer
    
    AppLogger.userAction('Menu item tapped', {
      'title': menuItem.name,
      'path': menuItem.routePath,
    });

    if (menuItem.routePath.isNotEmpty) {
      context.go(menuItem.routePath);
    } else {
      // Handle external URLs or special actions
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${menuItem.name} feature coming soon!'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Widget _buildFooter(BuildContext context, AuthService authService) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Settings
          ListTile(
            leading: const Icon(
              Icons.settings_outlined,
              color: AppTheme.textSecondary,
            ),
            title: Text(
              'Settings',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            onTap: () {
              Navigator.of(context).pop();
              AppLogger.userAction('Settings tapped');
              // Navigate to settings
            },
          ),
          
          // Logout
          ListTile(
            leading: const Icon(
              Icons.logout,
              color: AppTheme.errorColor,
            ),
            title: Text(
              'Logout',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.errorColor,
              ),
            ),
            onTap: () async {
              Navigator.of(context).pop();
              
              final shouldLogout = await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Logout'),
                  content: const Text('Are you sure you want to logout?'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.errorColor,
                      ),
                      child: const Text('Logout'),
                    ),
                  ],
                ),
              );

              if (shouldLogout == true) {
                AppLogger.userAction('Logout confirmed from drawer');
                await authService.logout();
              }
            },
          ),
          
          const SizedBox(height: 8),
          
          // App Version
          Text(
            'Version 1.0.0',
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getMenuIcon(String? iconName) {
    switch (iconName?.toLowerCase()) {
      case 'dashboard':
        return Icons.dashboard_outlined;
      case 'students':
      case 'student':
        return Icons.people_outline;
      case 'courses':
      case 'course':
        return Icons.book_outlined;
      case 'assignments':
      case 'assignment':
        return Icons.assignment_outlined;
      case 'tests':
      case 'test':
        return Icons.quiz_outlined;
      case 'results':
      case 'result':
        return Icons.assessment_outlined;
      case 'attendance':
        return Icons.event_available_outlined;
      case 'reports':
      case 'report':
        return Icons.analytics_outlined;
      case 'settings':
        return Icons.settings_outlined;
      case 'profile':
        return Icons.person_outline;
      case 'notifications':
        return Icons.notifications_outlined;
      case 'calendar':
        return Icons.calendar_today_outlined;
      case 'messages':
        return Icons.message_outlined;
      case 'library':
        return Icons.library_books_outlined;
      case 'fees':
        return Icons.payment_outlined;
      case 'transport':
        return Icons.directions_bus_outlined;
      case 'faculty':
        return Icons.school_outlined;
      case 'admin':
        return Icons.admin_panel_settings_outlined;
      default:
        return Icons.menu_outlined;
    }
  }
}
