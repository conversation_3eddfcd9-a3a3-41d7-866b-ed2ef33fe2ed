import 'package:flutter/material.dart';

class UserProvider with ChangeNotifier {
  String? _userId;
  String? _username;
  int? _credits;
  String? _sessionId;

  String? get userId => _userId;
  String? get username => _username;
  int? get credits => _credits;
  String? get sessionId => _sessionId;

  // Method to set all user data
  void setUserData({
    required String userId,
    required String username,
    required int credits,
    required String sessionId,
  }) {
    _userId = userId;
    _username = username;
    _credits = credits;
    _sessionId = sessionId;
    notifyListeners(); // Notify listeners when data changes
  }

  // Method to set userId
  void setUserId(String userId) {
    _userId = userId;
    notifyListeners();
  }

  // Method to clear user data
  void clearUserData() {
    _userId = null;
    _username = null;
    _credits = null;
    _sessionId = null;
    notifyListeners();
  }

  void setSessionId(String sessionId) {
    _sessionId = sessionId;
    notifyListeners();
  }

  // Method to clear only userId (if needed)
  void clearUserId() {
    _userId = null;
    notifyListeners();
  }

  // Method to set credits
  void setCredits(int credits) {
    _credits = credits;
    notifyListeners();
  }
}
