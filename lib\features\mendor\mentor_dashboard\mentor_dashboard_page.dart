import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class MentorDashboardPage extends StatelessWidget {
  const MentorDashboardPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'Mentor Dashboard',
      subtitle: 'Guide and support your students',
      breadcrumbs: ['Dashboard', 'Mendor', 'Mentor Dashboard'],
      featureName: 'Mentor Dashboard',
    );
  }
}
